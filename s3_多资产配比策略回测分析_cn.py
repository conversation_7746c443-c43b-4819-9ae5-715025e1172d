#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
多资产配比策略回测分析脚本
功能：对指定权重的多资产组合进行回测分析，计算各种风险收益指标
作者：AI Assistant
日期：2025-01-19
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib
import warnings
from datetime import datetime
warnings.filterwarnings('ignore')

# 设置中文字体
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
matplotlib.rcParams['axes.unicode_minus'] = False

# ==================== 配置参数 ====================

# 策略配置 - 资产组合1（股债组合）
STRATEGY_1 = {
    'name': '中证股债组合(60/40)',
    'assets': ['中证800全收益', '中证全债'],
    'weights': [0.6, 0.4]  # 权重总和必须为1.0
}

# 策略配置 - 资产组合2（股债金组合）
STRATEGY_2 = {
    'name': '中证股债金组合(50/30/20)',
    'assets': ['中证800全收益', '中证全债', 'COMEX黄金'],
    'weights': [0.5, 0.3, 0.2]  # 权重总和必须为1.0
}

# 策略配置 - 资产组合3（股债币组合，可选）
STRATEGY_3 = {
    'name': '中证股债币组合(55/40/5)',
    'assets': ['中证800全收益', '中证全债', 'MVDA Index'],
    'weights': [0.55, 0.4, 0.05]  # 权重总和必须为1.0
}

# 设置为 None 可禁用第三个策略
# STRATEGY_3 = None

# 分析参数
RISK_FREE_RATE = 0.00  # 无风险利率（年化）

# 时间区间设置
START_DATE = "2015-01-01"  # 回测开始时间，格式：YYYY-MM-DD（设为None使用全部数据）
# START_DATE = None  # 取消注释此行使用全部数据

# 数据文件路径
DATA_FILE = "合并指数数据_2014起.xlsx"

# 输出文件路径
OUTPUT_FILE = "中证多资产配比策略回测分析结果.xlsx"

def load_and_preprocess_data(file_path, start_date=None):
    """读取并预处理数据"""
    print("正在读取数据文件...")
    
    try:
        # 读取Excel文件
        raw_data = pd.read_excel(file_path)
        print(f"成功读取数据，原始数据形状: {raw_data.shape}")
        
        # 日期列处理
        date_col = raw_data.columns[0]
        print(f"使用日期列: {date_col}")
        
        # 转换日期格式
        raw_data[date_col] = pd.to_datetime(raw_data[date_col])
        raw_data = raw_data.set_index(date_col)
        
        # 如果指定了开始日期，则筛选数据
        if start_date is not None:
            start_date = pd.to_datetime(start_date)
            original_length = len(raw_data)
            raw_data = raw_data[raw_data.index >= start_date]
            filtered_length = len(raw_data)
            print(f"应用开始日期筛选 ({start_date.strftime('%Y-%m-%d')})：从 {original_length} 行减少到 {filtered_length} 行")
        
        # 重新索引为连续日期并向前填充
        print("正在进行日期补全处理...")
        index_new = pd.date_range(start=raw_data.index[0], end=raw_data.index[-1], freq='D') 
        raw_data = raw_data.reindex(index_new)  
        raw_data = raw_data.fillna(method='ffill')
        
        print(f"日期补全后数据形状: {raw_data.shape}")
        print(f"数据日期范围: {raw_data.index[0]} 到 {raw_data.index[-1]}")
        
        return raw_data
        
    except Exception as e:
        print(f"读取数据时发生错误: {e}")
        return None

def validate_strategy_config(strategy, data_columns):
    """验证策略配置的有效性"""
    if strategy is None:
        return False, "策略配置为空"
    
    required_keys = ['name', 'assets', 'weights']
    for key in required_keys:
        if key not in strategy:
            return False, f"策略配置缺少必需字段: {key}"
    
    # 检查权重数量是否与资产数量匹配
    if len(strategy['assets']) != len(strategy['weights']):
        return False, f"资产数量({len(strategy['assets'])})与权重数量({len(strategy['weights'])})不匹配"
    
    # 检查权重总和是否为1.0
    weight_sum = sum(strategy['weights'])
    if abs(weight_sum - 1.0) > 1e-6:
        return False, f"权重总和({weight_sum:.6f})不等于1.0"
    
    # 检查资产是否存在于数据中
    missing_assets = [asset for asset in strategy['assets'] if asset not in data_columns]
    if missing_assets:
        return False, f"以下资产不存在于数据中: {missing_assets}"
    
    return True, "配置有效"

def calculate_portfolio_nav(data, assets, weights, strategy_name):
    """计算投资组合净值序列"""
    print(f"正在计算 {strategy_name} 的净值序列...")
    
    # 提取相关资产的价格数据
    price_data = data[assets].copy()
    
    # 删除任何包含 NaN 的行
    original_length = len(price_data)
    price_data = price_data.dropna()
    dropped_rows = original_length - len(price_data)
    
    if dropped_rows > 0:
        print(f"删除了 {dropped_rows} 行包含 NaN 的数据")
    
    # 计算日收益率
    returns_data = price_data.pct_change().dropna()
    
    # 计算加权收益率
    portfolio_returns = (returns_data * weights).sum(axis=1)
    
    # 计算累积净值（以1为起始值）
    portfolio_nav = (1 + portfolio_returns).cumprod()
    
    print(f"{strategy_name} 净值计算完成，有效数据点: {len(portfolio_nav)}")
    print(f"期末净值: {portfolio_nav.iloc[-1]:.4f}")
    
    return portfolio_nav, portfolio_returns

def calculate_performance_metrics(nav_series, returns_series, strategy_name):
    """计算投资组合的绩效指标"""
    print(f"正在计算 {strategy_name} 的绩效指标...")
    
    # 基本统计
    total_days = len(nav_series)
    total_years = total_days / 252  # 假设一年252个交易日
    
    # 年化收益率
    total_return = nav_series.iloc[-1] - 1
    annualized_return = (1 + total_return) ** (1 / total_years) - 1
    
    # 年化波动率
    annualized_volatility = returns_series.std() * np.sqrt(252)
    
    # 夏普比率
    sharpe_ratio = (annualized_return - RISK_FREE_RATE) / annualized_volatility
    
    # 最大回撤计算
    running_max = nav_series.expanding().max()
    drawdown = (nav_series - running_max) / running_max
    max_drawdown = drawdown.min()
    max_drawdown_date = drawdown.idxmin()
    
    # 卡玛比率
    calmar_ratio = annualized_return / abs(max_drawdown) if max_drawdown != 0 else np.inf
    
    # 月度收益率成功率
    monthly_returns = returns_series.resample('M').sum()
    positive_months = (monthly_returns > 0).sum()
    total_months = len(monthly_returns)
    monthly_success_rate = positive_months / total_months if total_months > 0 else 0
    
    metrics = {
        '策略名称': strategy_name,
        '期末净值': nav_series.iloc[-1],
        '总收益率(%)': total_return * 100,
        '年化收益率(%)': annualized_return * 100,
        '年化波动率(%)': annualized_volatility * 100,
        '夏普比率': sharpe_ratio,
        '最大回撤(%)': max_drawdown * 100,
        '最大回撤日期': max_drawdown_date,
        '卡玛比率': calmar_ratio,
        '月度成功率(%)': monthly_success_rate * 100,
        '回测天数': total_days,
        '回测年数': total_years
    }
    
    print(f"{strategy_name} 绩效指标计算完成")
    return metrics, drawdown

def calculate_annual_performance(nav_series, returns_series, strategy_name):
    """计算年度绩效分析"""
    print(f"正在计算 {strategy_name} 的年度绩效...")
    
    annual_results = []
    
    # 按年份分组
    years = nav_series.index.year.unique()
    
    for year in years:
        year_data = nav_series[nav_series.index.year == year]
        year_returns = returns_series[returns_series.index.year == year]
        
        if len(year_data) < 2:  # 跳过数据不足的年份
            continue
        
        # 年度收益率
        year_start_nav = year_data.iloc[0]
        year_end_nav = year_data.iloc[-1]
        year_return = (year_end_nav / year_start_nav) - 1
        
        # 年度波动率（年化）
        year_volatility = year_returns.std() * np.sqrt(252)
        
        # 年度最大回撤
        year_running_max = year_data.expanding().max()
        year_drawdown = (year_data - year_running_max) / year_running_max
        year_max_drawdown = year_drawdown.min()
        
        # 年度夏普比率
        year_sharpe = (year_return - RISK_FREE_RATE) / year_volatility if year_volatility != 0 else 0
        
        # 月度成功率
        year_monthly_returns = year_returns.resample('M').sum()
        year_positive_months = (year_monthly_returns > 0).sum()
        year_total_months = len(year_monthly_returns)
        year_monthly_success_rate = year_positive_months / year_total_months if year_total_months > 0 else 0
        
        # 卡玛比率
        year_calmar = year_return / abs(year_max_drawdown) if year_max_drawdown != 0 else np.inf
        
        annual_results.append({
            '年份': year,
            '策略名称': strategy_name,
            '年度收益率(%)': year_return * 100,
            '年化波动率(%)': year_volatility * 100,
            '夏普比率': year_sharpe,
            '最大回撤(%)': year_max_drawdown * 100,
            '卡玛比率': year_calmar,
            '月度成功率(%)': year_monthly_success_rate * 100,
            '交易天数': len(year_data)
        })
    
    return pd.DataFrame(annual_results)

def plot_nav_and_drawdown(nav_data_dict, drawdown_data_dict):
    """绘制净值曲线和回撤曲线"""
    print("正在绘制净值曲线和回撤曲线...")
    
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(14, 10))
    
    # 定义颜色
    colors = ['blue', 'red', 'green', 'orange', 'purple']
    
    # 绘制净值曲线
    for i, (strategy_name, nav_series) in enumerate(nav_data_dict.items()):
        ax1.plot(nav_series.index, nav_series.values, 
                color=colors[i % len(colors)], linewidth=2, 
                label=f'{strategy_name}', alpha=0.8)
    
    ax1.set_title('多资产配比策略净值曲线对比', fontsize=14, fontweight='bold')
    ax1.set_ylabel('净值', fontsize=12)
    ax1.legend(fontsize=10, loc='upper left')
    ax1.grid(True, alpha=0.3)
    
    # 绘制回撤曲线
    for i, (strategy_name, drawdown_series) in enumerate(drawdown_data_dict.items()):
        ax2.plot(drawdown_series.index, drawdown_series.values * 100, 
                color=colors[i % len(colors)], linewidth=2, 
                label=f'{strategy_name}', alpha=0.8)
    
    ax2.set_title('回撤曲线对比', fontsize=14, fontweight='bold')
    ax2.set_xlabel('日期', fontsize=12)
    ax2.set_ylabel('回撤 (%)', fontsize=12)
    ax2.legend(fontsize=10, loc='lower right')
    ax2.grid(True, alpha=0.3)
    ax2.axhline(y=0, color='black', linestyle='-', alpha=0.3)
    
    plt.tight_layout()
    
    # 保存图片
    filename = '中证多资产配比策略回测分析.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    plt.show()
    
    print(f"图表已保存为: {filename}")

def save_results_to_excel(nav_data_dict, performance_dict, annual_performance_dict, output_file):
    """保存结果到Excel文件"""
    print("正在保存分析结果到Excel文件...")
    
    try:
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            
            # 1. 净值曲线数据表（所有策略在一个表中）
            nav_df = pd.DataFrame(nav_data_dict)
            nav_df.to_excel(writer, sheet_name='净值曲线对比', index_label='日期')
            
            # 2. 整体绩效摘要表（所有策略在一个表中）
            performance_df = pd.DataFrame(list(performance_dict.values()))
            performance_df.to_excel(writer, sheet_name='整体绩效摘要', index=False)
            
            # 3. 按策略名称创建独立的工作表
            for strategy_name in nav_data_dict.keys():
                # 创建策略专用的工作表名称（去除可能的特殊字符）
                safe_sheet_name = strategy_name.replace('/', '_').replace('\\', '_')[:31]  # Excel工作表名称限制31字符
                
                # 策略净值序列
                strategy_nav_df = pd.DataFrame({
                    '日期': nav_data_dict[strategy_name].index,
                    '净值': nav_data_dict[strategy_name].values
                })
                strategy_nav_df.to_excel(writer, sheet_name=f'{safe_sheet_name}_净值', index=False)
                
                # 策略整体绩效指标（单独工作表）
                strategy_performance = pd.DataFrame([performance_dict[strategy_name]]).T
                strategy_performance.columns = ['数值']
                strategy_performance.reset_index(inplace=True)
                strategy_performance.columns = ['指标名称', '数值']
                strategy_performance.to_excel(writer, sheet_name=f'{safe_sheet_name}_整体绩效', index=False)
                
                # 策略年度绩效分析（按用户要求的表格格式）
                if strategy_name in annual_performance_dict:
                    annual_df = annual_performance_dict[strategy_name]
                    if not annual_df.empty:
                        # 按用户要求的格式重新组织年度数据
                        formatted_annual = annual_df[['年份', '年度收益率(%)', '夏普比率', '最大回撤(%)', '月度成功率(%)']].copy()
                        formatted_annual.columns = ['年份', '组合收益率(%)', '夏普比率', '最大回撤(%)', '月度成功率(%)']
                        
                        # 计算全部期间的汇总行
                        total_performance = performance_dict[strategy_name]
                        summary_row = {
                            '年份': '全部',
                            '组合收益率(%)': total_performance['年化收益率(%)'],
                            '夏普比率': total_performance['夏普比率'],
                            '最大回撤(%)': abs(total_performance['最大回撤(%)']),  # 取绝对值，去掉负号
                            '月度成功率(%)': total_performance['月度成功率(%)']
                        }
                        
                        # 将汇总行添加到最后
                        formatted_annual = pd.concat([formatted_annual, pd.DataFrame([summary_row])], ignore_index=True)
                        formatted_annual.to_excel(writer, sheet_name=f'{safe_sheet_name}_年度分析', index=False)
                
                # 策略月度收益率
                returns_series = nav_data_dict[strategy_name].pct_change().dropna()
                monthly_returns = returns_series.resample('M').sum()
                monthly_df = pd.DataFrame({
                    '月份': monthly_returns.index,
                    '月度收益率': monthly_returns.values,
                    '月度收益率(%)': monthly_returns.values * 100
                })
                monthly_df.to_excel(writer, sheet_name=f'{safe_sheet_name}_月度', index=False)
            
            # 4. 月度收益率对比表（所有策略在一个表中）
            monthly_stats = []
            for strategy_name, nav_series in nav_data_dict.items():
                returns_series = nav_series.pct_change().dropna()
                monthly_returns = returns_series.resample('M').sum()
                monthly_returns.name = strategy_name
                monthly_stats.append(monthly_returns)
            
            if monthly_stats:
                monthly_df = pd.concat(monthly_stats, axis=1)
                monthly_df.index.name = '月份'
                monthly_df.to_excel(writer, sheet_name='月度收益率对比')
        
        print(f"分析结果已保存到: {output_file}")
        
    except Exception as e:
        print(f"保存Excel文件时发生错误: {e}")

def print_performance_summary(performance_dict):
    """打印绩效摘要"""
    print("\n" + "=" * 100)
    print("多资产配比策略回测分析结果摘要")
    print("=" * 100)
    
    for strategy_name, metrics in performance_dict.items():
        print(f"\n【{strategy_name}】")
        print(f"  期末净值: {metrics['期末净值']:.4f}")
        print(f"  总收益率: {metrics['总收益率(%)']:.2f}%")
        print(f"  年化收益率: {metrics['年化收益率(%)']:.2f}%")
        print(f"  年化波动率: {metrics['年化波动率(%)']:.2f}%")
        print(f"  夏普比率: {metrics['夏普比率']:.4f}")
        print(f"  最大回撤: {metrics['最大回撤(%)']:.2f}%")
        print(f"  回撤发生日期: {metrics['最大回撤日期']}")
        print(f"  卡玛比率: {metrics['卡玛比率']:.4f}")
        print(f"  月度成功率: {metrics['月度成功率(%)']:.1f}%")
        print(f"  回测期间: {metrics['回测年数']:.1f}年")
    
    print("\n" + "=" * 100)

def main():
    """主函数：执行完整的回测分析流程"""
    print("=" * 100)
    print("多资产配比策略回测分析脚本")
    print("=" * 100)
    
    # 收集所有有效的策略配置
    strategies = []
    if STRATEGY_1 is not None:
        strategies.append(STRATEGY_1)
    if STRATEGY_2 is not None:
        strategies.append(STRATEGY_2)
    if STRATEGY_3 is not None:
        strategies.append(STRATEGY_3)
    
    if not strategies:
        print("错误：没有配置任何策略")
        return
    
    print(f"配置的策略数量: {len(strategies)}")
    for i, strategy in enumerate(strategies, 1):
        print(f"策略{i}: {strategy['name']} - 资产: {strategy['assets']} - 权重: {strategy['weights']}")
    
    print("=" * 100)

    # 1. 读取和预处理数据
    if START_DATE is not None:
        print(f"回测开始日期设置为: {START_DATE}")
    else:
        print("使用全部数据进行回测")
        
    data = load_and_preprocess_data(DATA_FILE, START_DATE)
    if data is None:
        print("数据读取失败，程序终止")
        return

    # 2. 验证策略配置
    valid_strategies = []
    for strategy in strategies:
        is_valid, message = validate_strategy_config(strategy, data.columns)
        if is_valid:
            print(f"✓ {strategy['name']}: {message}")
            valid_strategies.append(strategy)
        else:
            print(f"✗ {strategy['name']}: {message}")
            print(f"  可用资产列表: {list(data.columns)}")
    
    if not valid_strategies:
        print("错误：没有有效的策略配置")
        return

    # 3. 计算各策略的净值序列
    nav_data_dict = {}
    returns_data_dict = {}
    
    for strategy in valid_strategies:
        nav_series, returns_series = calculate_portfolio_nav(
            data, strategy['assets'], strategy['weights'], strategy['name']
        )
        nav_data_dict[strategy['name']] = nav_series
        returns_data_dict[strategy['name']] = returns_series

    # 4. 确保所有策略使用相同的时间范围
    print("\n正在对齐所有策略的时间范围...")
    
    # 找到所有策略的共同时间区间
    common_dates = None
    for nav_series in nav_data_dict.values():
        if common_dates is None:
            common_dates = nav_series.index
        else:
            common_dates = common_dates.intersection(nav_series.index)
    
    if len(common_dates) == 0:
        print("错误：策略间没有共同的时间期间")
        return
    
    # 对齐所有策略的数据
    aligned_nav_dict = {}
    aligned_returns_dict = {}
    
    for strategy_name in nav_data_dict.keys():
        aligned_nav_dict[strategy_name] = nav_data_dict[strategy_name].loc[common_dates]
        aligned_returns_dict[strategy_name] = returns_data_dict[strategy_name].loc[common_dates]
    
    print(f"共同时间期间: {len(common_dates)}天")
    print(f"回测期间: {common_dates.min()} 到 {common_dates.max()}")

    # 5. 计算绩效指标
    performance_dict = {}
    drawdown_dict = {}
    
    for strategy_name in aligned_nav_dict.keys():
        metrics, drawdown = calculate_performance_metrics(
            aligned_nav_dict[strategy_name], 
            aligned_returns_dict[strategy_name], 
            strategy_name
        )
        performance_dict[strategy_name] = metrics
        drawdown_dict[strategy_name] = drawdown

    # 6. 计算年度绩效
    annual_performance_dict = {}
    
    for strategy_name in aligned_nav_dict.keys():
        annual_df = calculate_annual_performance(
            aligned_nav_dict[strategy_name],
            aligned_returns_dict[strategy_name],
            strategy_name
        )
        annual_performance_dict[strategy_name] = annual_df

    # 7. 绘制图表
    plot_nav_and_drawdown(aligned_nav_dict, drawdown_dict)

    # 8. 保存结果到Excel
    save_results_to_excel(aligned_nav_dict, performance_dict, annual_performance_dict, OUTPUT_FILE)

    # 9. 打印结果摘要
    print_performance_summary(performance_dict)

    print(f"\n回测分析完成！")
    print(f"Excel文件: {OUTPUT_FILE}")
    print(f"图片文件: 多资产配比策略回测分析.png")

if __name__ == "__main__":
    main() 