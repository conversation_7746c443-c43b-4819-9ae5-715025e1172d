#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
黄金避险属性分析脚本
功能：对比分析黄金和比特币的避险特性，使用现代金融计量方法进行严格的统计检验
基于Baur & Lucey (2010)等学术研究方法，采用GARCH回归模型和事件研究法
作者：AI Assistant
日期：2025-01-27
版本：2.0
"""

import pandas as pd
import numpy as np
import warnings
from datetime import datetime
warnings.filterwarnings('ignore')

# 导入金融计量分析库
try:
    from arch import arch_model
    print("✓ 已导入 arch 库用于 GARCH 建模")
    ARCH_AVAILABLE = True
except ImportError:
    print("⚠ 警告：未安装 arch 库，将使用 OLS 回归模型")
    arch_model = None
    ARCH_AVAILABLE = False

# 导入统计检验库
try:
    import statsmodels.api as sm
    from scipy import stats
    print("✓ 已导入统计分析库")
except ImportError as e:
    print(f"❌ 统计分析库导入失败: {e}")
    raise

# ==================== 配置参数 ====================
# 数据文件路径
DATA_FILE = "合并指数数据_2014起.xlsx"

# 资产分组配置（基于学术研究标准）
SAFE_ASSETS = ["COMEX黄金", "Bitcoin"]  # 避险资产候选
RISK_ASSETS_STOCK = ["中证800全收益", "MSCI全球市场股票全收益"]  # 风险资产-股票
RISK_ASSETS_BOND = ["中证全债", "巴克莱彭博全球债"]  # 风险资产-债券
ALL_RISK_ASSETS = RISK_ASSETS_STOCK + RISK_ASSETS_BOND

# 中文资产名称映射（用于结果展示）
ASSET_NAME_MAPPING = {
    "COMEX黄金": "COMEX黄金",
    "Bitcoin": "比特币",
    "MVDA Index": "加密货币指数",
    "中证800全收益": "中证800全收益指数",
    "MSCI全球市场股票全收益": "MSCI全球股票指数",
    "中证全债": "中证全债指数",
    "巴克莱彭博全球债": "巴克莱全球债券指数"
}

# 分析参数（基于Baur & Lucey 2010等研究）
QUANTILES = [0.01, 0.025, 0.05]  # 极端分位数阈值：1%, 2.5%, 5%
EVENT_WINDOW_LENGTH = 16  # 事件窗口长度 [0, +15]
SIGNIFICANCE_LEVEL = 0.05  # 统计显著性水平
RISK_FREE_RATE = 0.00  # 无风险利率（年化）

# 模型配置
GARCH_P = 1  # GARCH模型滞后阶数
GARCH_Q = 1  # ARCH模型滞后阶数
MAX_ITERATIONS = 1000  # 最大迭代次数

# 输出文件配置
TIMESTAMP = datetime.now().strftime("%Y%m%d_%H%M%S")

def load_and_preprocess_data(file_path):
    """
    读取并预处理数据
    完全复用参考脚本中的数据加载和日期补全处理代码

    Parameters:
    -----------
    file_path : str
        数据文件路径

    Returns:
    --------
    pd.DataFrame
        预处理后的数据，包含日期补全和向前填充
    """
    print("📊 正在读取数据文件...")

    try:
        # 读取Excel文件
        raw_data = pd.read_excel(file_path)
        print(f"✓ 成功读取数据，原始数据形状: {raw_data.shape}")

        # 日期列处理
        date_col = raw_data.columns[0]
        print(f"✓ 使用日期列: {date_col}")

        # 转换日期格式
        raw_data[date_col] = pd.to_datetime(raw_data[date_col])
        raw_data = raw_data.set_index(date_col)

        # 重新索引为连续日期并向前填充（完全复用参考脚本方法）
        print("📅 正在进行日期补全处理...")
        index_new = pd.date_range(start=raw_data.index[0], end=raw_data.index[-1], freq='D')
        raw_data = raw_data.reindex(index_new)
        raw_data = raw_data.fillna(method='ffill')

        print(f"✓ 日期补全后数据形状: {raw_data.shape}")
        print(f"✓ 数据日期范围: {raw_data.index[0].strftime('%Y-%m-%d')} 到 {raw_data.index[-1].strftime('%Y-%m-%d')}")

        # 数据质量检查
        missing_data = raw_data.isnull().sum()
        if missing_data.sum() > 0:
            print(f"⚠ 发现缺失数据: {missing_data[missing_data > 0].to_dict()}")
        else:
            print("✓ 数据质量检查通过，无缺失值")

        return raw_data

    except Exception as e:
        print(f"❌ 读取数据时发生错误: {e}")
        return None

def check_asset_availability(data, asset_lists):
    """
    检查资产可用性并提供详细的诊断信息

    Parameters:
    -----------
    data : pd.DataFrame
        原始数据
    asset_lists : dict
        资产分组字典

    Returns:
    --------
    dict
        可用资产字典
    """
    available_columns = data.columns.tolist()
    print(f"\n📋 数据中可用的资产列 (共{len(available_columns)}个):")
    for i, col in enumerate(available_columns, 1):
        print(f"  {i:2d}. {col}")

    results = {}
    total_missing = 0

    for name, assets in asset_lists.items():
        available = [asset for asset in assets if asset in available_columns]
        missing = [asset for asset in assets if asset not in available_columns]

        if missing:
            print(f"⚠ {name} 中缺失资产: {missing}")
            total_missing += len(missing)
        else:
            print(f"✓ {name} 资产完整: {available}")

        results[name] = available

    if total_missing == 0:
        print("✓ 所有配置的资产都可用")
    else:
        print(f"⚠ 总计缺失 {total_missing} 个资产")

    return results

def calculate_returns(data, asset_list):
    """
    计算资产组的收益率
    完全复用参考脚本中的收益率计算方法

    Parameters:
    -----------
    data : pd.DataFrame
        价格数据
    asset_list : list
        资产列表

    Returns:
    --------
    pd.DataFrame
        日收益率数据
    """
    print(f"📈 正在计算收益率，资产: {asset_list}")

    # 检查哪些资产在数据中存在
    available_assets = [asset for asset in asset_list if asset in data.columns and asset.strip() != ""]
    missing_assets = [asset for asset in asset_list if asset not in data.columns or asset.strip() == ""]

    if missing_assets:
        print(f"⚠ 以下资产不存在或为空，将被忽略: {missing_assets}")

    if not available_assets:
        print("❌ 错误：没有可用的资产")
        return None

    print(f"✓ 使用的资产: {available_assets}")

    # 提取相关资产的价格数据
    price_data = data[available_assets].copy()

    # 删除任何包含 NaN 的行
    original_length = len(price_data)
    price_data = price_data.dropna()
    dropped_rows = original_length - len(price_data)

    if dropped_rows > 0:
        print(f"🧹 删除了 {dropped_rows} 行包含 NaN 的数据")

    # 计算日收益率（使用 pct_change 方法）
    returns_data = price_data.pct_change().dropna()

    # 再次检查并删除任何剩余的 NaN 值
    before_final_clean = len(returns_data)
    returns_data = returns_data.dropna()
    final_dropped = before_final_clean - len(returns_data)

    if final_dropped > 0:
        print(f"🧹 计算收益率后删除了额外的 {final_dropped} 行 NaN 数据")

    # 数据统计摘要
    print(f"✓ 收益率计算完成，有效数据点: {len(returns_data)}")
    if len(returns_data) > 0:
        print(f"✓ 数据期间: {returns_data.index[0].strftime('%Y-%m-%d')} 到 {returns_data.index[-1].strftime('%Y-%m-%d')}")

        # 显示基本统计信息
        for asset in available_assets:
            if asset in returns_data.columns:
                mean_return = returns_data[asset].mean() * 252  # 年化
                volatility = returns_data[asset].std() * np.sqrt(252)  # 年化
                print(f"  {ASSET_NAME_MAPPING.get(asset, asset)}: 年化收益率={mean_return:.2%}, 年化波动率={volatility:.2%}")

    return returns_data

def align_data(returns_dict):
    """对齐所有资产组的数据时间范围"""
    print("\n正在对齐数据的时间范围...")
    
    # 找到所有数据集的共同日期
    common_dates = None
    for name, returns in returns_dict.items():
        if returns is not None:
            if common_dates is None:
                common_dates = returns.index
            else:
                common_dates = common_dates.intersection(returns.index)
    
    if common_dates is None or len(common_dates) == 0:
        print("错误：没有共同的时间期间")
        return None
    
    # 对齐数据
    aligned_returns = {}
    for name, returns in returns_dict.items():
        if returns is not None:
            aligned_returns[name] = returns.loc[common_dates]
    
    # 删除任何剩余的 NaN 行
    print("正在删除对齐后的 NaN 数据...")
    
    # 合并所有数据来检查 NaN
    combined_data = pd.concat(aligned_returns.values(), axis=1)
    clean_dates = combined_data.dropna().index
    
    # 使用清洁的日期重新对齐
    final_returns = {}
    for name, returns in aligned_returns.items():
        final_returns[name] = returns.loc[clean_dates]
    
    dropped_dates = len(common_dates) - len(clean_dates)
    if dropped_dates > 0:
        print(f"对齐后删除了 {dropped_dates} 行包含 NaN 的数据")
    
    print(f"最终共同数据期间: {len(clean_dates)}天")
    if len(clean_dates) > 0:
        print(f"分析期间: {clean_dates.min()} 到 {clean_dates.max()}")
    
    return final_returns

def create_interaction_variables(returns, quantiles=QUANTILES):
    """
    创建交互变量用于识别极端市场事件
    基于Baur & Lucey (2010)方法，创建条件交互变量

    Parameters:
    -----------
    returns : pd.Series
        风险资产收益率序列
    quantiles : list
        分位数阈值列表

    Returns:
    --------
    dict
        交互变量字典，包含虚拟变量和交互项
    """
    print(f"🎯 正在创建交互变量，分位数阈值: {[f'{q:.1%}' for q in quantiles]}")

    interactions = {}

    for q in quantiles:
        threshold = returns.quantile(q)
        interaction_name = f'D_{int(q*100)}pct'
        interaction_term_name = f'D_{int(q*100)}pct_interaction'
        # 创建虚拟变量：当收益率低于阈值时为1，否则为0
        dummy = np.where(returns <= threshold, 1, 0)

        # 创建交互项：虚拟变量 × 收益率（用于回归分析）
        interaction_term = np.where(returns <= threshold, returns, 0)

        # 存储虚拟变量和交互项
        interactions[interaction_name] = dummy
        interactions[interaction_term_name] = interaction_term

        # 记录极端事件日期
        extreme_dates = returns[returns <= threshold].index
        interactions[f'{interaction_name}_dates'] = extreme_dates

        # 统计信息
        trigger_count = dummy.sum()
        trigger_rate = trigger_count / len(returns)

        print(f"  📊 {interaction_name}: 阈值={threshold:.4f}, 触发次数={trigger_count}, 触发率={trigger_rate:.2%}")

        # 显示极端事件的统计特征
        extreme_returns = returns[returns <= threshold]
        if len(extreme_returns) > 0:
            print(f"     极端收益率统计: 均值={extreme_returns.mean():.4f}, 标准差={extreme_returns.std():.4f}")

    print(f"✓ 交互变量创建完成，共生成 {len(interactions)} 个变量")
    return interactions

def estimate_garch_model(dependent_var, independent_vars, model_name):
    """
    估计GARCH回归模型，自动降级为OLS
    基于Baur & Lucey (2010)的回归方程：
    R_safe,t = α + β1*R_stock,t + β2*D_q*R_stock,t + ε_t

    Parameters:
    -----------
    dependent_var : pd.Series
        因变量（避险资产收益率）
    independent_vars : pd.DataFrame
        自变量（包含风险资产收益率和交互项）
    model_name : str
        模型名称（用于日志输出）

    Returns:
    --------
    dict
        回归结果字典，包含参数、标准误、t统计量、p值等
    """
    print(f"🔬 正在估计模型: {model_name}")

    try:
        # 首先尝试GARCH模型（如果arch库可用）
        if ARCH_AVAILABLE:
            try:
                # 注意：arch库的多元GARCH实现复杂，这里先用OLS
                print("  ⚠ arch库的多元GARCH较复杂，使用OLS回归")
                raise NotImplementedError("使用OLS替代")
            except Exception as e:
                print(f"  ⚠ GARCH模型失败，降级为OLS: {str(e)[:50]}...")

        # OLS回归（主要方法）
        X = sm.add_constant(independent_vars)
        model = sm.OLS(dependent_var, X)
        results = model.fit()

        params = results.params
        std_errors = results.bse
        t_stats = results.tvalues
        p_values = results.pvalues

        # 计算β1 + β2的联合检验
        beta_sum_test = None
        if len(params) >= 3:  # 至少有常数项、β1、β2
            try:
                # 构建约束矩阵进行联合检验
                constraint_matrix = np.zeros((1, len(params)))
                constraint_matrix[0, 1] = 1  # β1系数
                constraint_matrix[0, 2] = 1  # β2系数

                # 进行Wald检验
                wald_test = results.wald_test(constraint_matrix)
                beta_sum_test = {
                    'beta_sum': params.iloc[1] + params.iloc[2],
                    'p_value': wald_test.pvalue
                }
            except Exception as e:
                print(f"    ⚠ β1+β2联合检验失败: {e}")

        result = {
            'params': params,
            'std_errors': std_errors,
            't_stats': t_stats,
            'p_values': p_values,
            'aic': results.aic,
            'bic': results.bic,
            'r_squared': results.rsquared,
            'adj_r_squared': results.rsquared_adj,
            'f_statistic': results.fvalue,
            'f_pvalue': results.f_pvalue,
            'model_type': 'OLS',
            'beta_sum_test': beta_sum_test,
            'n_obs': results.nobs
        }

        print(f"  ✓ 模型估计完成: R² = {results.rsquared:.4f}, F统计量 = {results.fvalue:.2f}")
        return result

    except Exception as e:
        print(f"❌ 模型估计失败 {model_name}: {e}")
        return None

def determine_hedge_safe_haven_properties(beta1, beta2, p_beta1, p_beta2, p_beta_sum, significance_level=SIGNIFICANCE_LEVEL):
    """
    判定避险属性
    基于Baur & Lucey (2010)的标准：
    - 对冲属性(Hedge): β1 ≤ 0 且统计上不显著 (p > 0.05)
    - 安全港属性(Safe Haven): β1 + β2 ≤ 0 且统计上显著 (p ≤ 0.05)

    Parameters:
    -----------
    beta1 : float
        风险资产收益率系数
    beta2 : float
        交互项系数
    p_beta1 : float
        β1的p值
    p_beta2 : float
        β2的p值
    p_beta_sum : float
        β1+β2联合检验的p值
    significance_level : float
        显著性水平

    Returns:
    --------
    dict
        避险属性判定结果
    """
    # 对冲属性判定：β1 ≤ 0 且统计上不显著
    is_hedge = (beta1 <= 0) and (p_beta1 > significance_level)

    # 安全港属性判定：β1 + β2 ≤ 0 且统计上显著
    beta_sum = beta1 + beta2
    is_safe_haven = (beta_sum <= 0) and (p_beta_sum is not None) and (p_beta_sum <= significance_level)

    # 分类结果
    if is_hedge and is_safe_haven:
        classification = "强避险资产"
        description = "既具有对冲属性又具有安全港属性"
    elif is_hedge:
        classification = "弱避险资产"
        description = "仅具有对冲属性"
    elif is_safe_haven:
        classification = "条件避险资产"
        description = "仅在极端市场条件下具有避险属性"
    else:
        classification = "非避险资产"
        description = "不具有避险属性"

    return {
        'classification': classification,
        'description': description,
        'is_hedge': is_hedge,
        'is_safe_haven': is_safe_haven,
        'beta1': beta1,
        'beta2': beta2,
        'beta_sum': beta_sum,
        'p_beta1': p_beta1,
        'p_beta2': p_beta2,
        'p_beta_sum': p_beta_sum
    }







def main():
    """主函数：执行黄金避险属性回归分析"""
    print("=" * 80)
    print("黄金避险属性回归分析")
    print("=" * 80)
    print(f"避险资产候选: {SAFE_ASSETS}")
    print(f"风险资产-股票: {RISK_ASSETS_STOCK}")
    print(f"风险资产-债券: {RISK_ASSETS_BOND}")
    print(f"极端分位数阈值: {[f'{q*100}%' for q in QUANTILES]}")
    print("=" * 80)

    # 1. 读取和预处理数据
    data = load_and_preprocess_data(DATA_FILE)
    if data is None:
        print("数据读取失败，程序终止")
        return

    # 2. 检查资产可用性
    asset_lists = {
        '避险资产': SAFE_ASSETS,
        '风险资产-股票': RISK_ASSETS_STOCK,
        '风险资产-债券': RISK_ASSETS_BOND
    }
    available_assets = check_asset_availability(data, asset_lists)

    # 3. 计算收益率
    returns_dict = {}
    for name, assets in available_assets.items():
        if assets:
            returns = calculate_returns(data, assets)
            if returns is not None:
                returns_dict[name] = returns

    # 如果没有风险资产-股票组，单独计算
    if '风险资产-股票' not in returns_dict:
        stock_returns = calculate_returns(data, RISK_ASSETS_STOCK)
        if stock_returns is not None:
            returns_dict['风险资产-股票'] = stock_returns

    # 4. 对齐数据
    aligned_returns = align_data(returns_dict)
    if aligned_returns is None:
        print("数据对齐失败，程序终止")
        return

    # 5. 创建交互变量
    print("\n正在创建极端事件交互变量...")
    interactions_dict = {}
    for risk_asset in ALL_RISK_ASSETS:
        if risk_asset in aligned_returns.get('风险资产-股票', pd.DataFrame()).columns:
            returns = aligned_returns['风险资产-股票'][risk_asset]
        elif risk_asset in aligned_returns.get('风险资产-债券', pd.DataFrame()).columns:
            returns = aligned_returns['风险资产-债券'][risk_asset]
        else:
            continue

        interactions_dict[risk_asset] = create_interaction_variables(returns, QUANTILES)

    # 6. 回归分析（核心部分）
    print("\n正在进行回归分析...")
    garch_results = {}
    hedge_results = {}

    for safe_asset in SAFE_ASSETS:
        if safe_asset in aligned_returns.get('避险资产', pd.DataFrame()).columns:
            safe_returns = aligned_returns['避险资产'][safe_asset]
            hedge_results[safe_asset] = {}

            for risk_asset in ALL_RISK_ASSETS:
                if risk_asset in interactions_dict:
                    risk_returns = None
                    if risk_asset in aligned_returns.get('风险资产-股票', pd.DataFrame()).columns:
                        risk_returns = aligned_returns['风险资产-股票'][risk_asset]
                    elif risk_asset in aligned_returns.get('风险资产-债券', pd.DataFrame()).columns:
                        risk_returns = aligned_returns['风险资产-债券'][risk_asset]

                    if risk_returns is not None:
                        hedge_results[safe_asset][risk_asset] = {}

                        for quantile in QUANTILES:
                            interaction_name = f'D_{int(quantile*100)}pct'
                            if interaction_name in interactions_dict[risk_asset]:
                                interaction_var = interactions_dict[risk_asset][interaction_name]

                                # 准备回归变量
                                X = pd.DataFrame({
                                    'risk_return': risk_returns,
                                    'interaction': interaction_var
                                })

                                # 估计模型
                                model_name = f"{safe_asset}_vs_{risk_asset}_{int(quantile*100)}pct"
                                result = estimate_garch_model(safe_returns, X, model_name)

                                if result is not None:
                                    garch_results[model_name] = result

                                    # 提取系数和显著性
                                    beta1 = result['params'].get('risk_return', 0)
                                    beta2 = result['params'].get('interaction', 0)
                                    p_beta1 = result['p_values'].get('risk_return', 1)
                                    p_beta2 = result['p_values'].get('interaction', 1)

                                    # 计算β1+β2的显著性（简化处理）
                                    p_beta_sum = min(p_beta1, p_beta2)  # 简化处理

                                    # 判定避险属性
                                    hedge_property = determine_hedge_safe_haven_properties(
                                        beta1, beta2, p_beta1, p_beta2, p_beta_sum)

                                    hedge_results[safe_asset][risk_asset][quantile] = {
                                        'beta1': beta1,
                                        'beta2': beta2,
                                        'p_beta1': p_beta1,
                                        'p_beta2': p_beta2,
                                        'p_beta_sum': p_beta_sum,
                                        'hedge_property': hedge_property,
                                        'is_hedge': (beta1 <= 0) and (p_beta1 > SIGNIFICANCE_LEVEL),
                                        'is_safe_haven': (beta1 + beta2 <= 0) and (p_beta_sum <= SIGNIFICANCE_LEVEL)
                                    }

    # 7. 输出回归结果
    print("\n" + "=" * 80)
    print("回归分析结果")
    print("=" * 80)

    # 输出详细的回归结果
    for model_name, result in garch_results.items():
        print(f"\n模型: {model_name}")
        print("-" * 60)
        print(f"模型类型: {result.get('model_type', 'Unknown')}")
        print(f"观测数: {int(result.get('n_obs', 0))}")
        print(f"R²: {result.get('r_squared', 0):.4f}")
        print(f"调整R²: {result.get('adj_r_squared', 0):.4f}")
        print(f"F统计量: {result.get('f_statistic', 0):.4f} (p值: {result.get('f_pvalue', 1):.4f})")
        print(f"AIC: {result.get('aic', 0):.2f}")
        print(f"BIC: {result.get('bic', 0):.2f}")

        print("\n回归系数:")
        params = result['params']
        std_errors = result['std_errors']
        t_stats = result['t_stats']
        p_values = result['p_values']

        for param_name in params.index:
            coef = params[param_name]
            se = std_errors[param_name]
            t_stat = t_stats[param_name]
            p_val = p_values[param_name]
            significance = "***" if p_val < 0.01 else "**" if p_val < 0.05 else "*" if p_val < 0.1 else ""

            print(f"  {param_name:15s}: {coef:8.6f} ({se:8.6f}) t={t_stat:6.2f} p={p_val:6.4f} {significance}")

        # β1+β2联合检验结果
        if result.get('beta_sum_test'):
            beta_sum = result['beta_sum_test']['beta_sum']
            beta_sum_p = result['beta_sum_test']['p_value']
            print(f"  β1+β2联合检验: {beta_sum:8.6f} (p值: {beta_sum_p:.4f})")

    # 输出避险属性判定结果
    print("\n" + "=" * 80)
    print("避险属性判定结果")
    print("=" * 80)

    for safe_asset, risk_results in hedge_results.items():
        print(f"\n{ASSET_NAME_MAPPING.get(safe_asset, safe_asset)} 避险属性分析:")
        print("-" * 60)

        for risk_asset, quantile_results in risk_results.items():
            print(f"\n  vs {ASSET_NAME_MAPPING.get(risk_asset, risk_asset)}:")

            for quantile, result in quantile_results.items():
                hedge_prop = result['hedge_property']
                print(f"    {quantile:.1%}分位数: β1={result['beta1']:7.4f} (p={result['p_beta1']:.3f}), "
                      f"β2={result['beta2']:7.4f} (p={result['p_beta2']:.3f}), "
                      f"β1+β2={result['beta1']+result['beta2']:7.4f}")
                print(f"                   对冲属性: {'是' if result['is_hedge'] else '否'}, "
                      f"安全港属性: {'是' if result['is_safe_haven'] else '否'}")
                print(f"                   分类: {hedge_prop['classification']} - {hedge_prop['description']}")

    print("\n" + "=" * 80)
    print("回归分析完成！")
    print("=" * 80)



if __name__ == "__main__":
    main()