#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
黄金避险属性分析脚本
功能：对比分析黄金和比特币的避险特性，使用现代金融计量方法进行严格的统计检验
基于Baur & Lucey (2010)等学术研究方法，采用GARCH回归模型和事件研究法
作者：AI Assistant
日期：2025-01-27
版本：2.0
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib
import warnings
from datetime import datetime
import os
warnings.filterwarnings('ignore')

# 导入金融计量分析库
try:
    from arch import arch_model
    print("✓ 已导入 arch 库用于 GARCH 建模")
    ARCH_AVAILABLE = True
except ImportError:
    print("⚠ 警告：未安装 arch 库，将使用 OLS 回归模型")
    arch_model = None
    ARCH_AVAILABLE = False

# 导入统计检验库
try:
    import statsmodels.api as sm
    from scipy import stats
    import seaborn as sns
    print("✓ 已导入统计分析库")
except ImportError as e:
    print(f"❌ 统计分析库导入失败: {e}")
    raise

# 导入Excel图表库
try:
    from openpyxl import load_workbook
    from openpyxl.chart import LineChart, Reference, ScatterChart
    from openpyxl.chart.axis import DateAxis
    from openpyxl.styles import Font, PatternFill, Alignment
    from openpyxl.utils.dataframe import dataframe_to_rows
    print("✓ 已导入 Excel 图表库")
    EXCEL_CHARTS_AVAILABLE = True
except ImportError:
    print("⚠ Excel 图表库不可用，将使用基础保存功能")
    EXCEL_CHARTS_AVAILABLE = False

# 设置中文字体
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
matplotlib.rcParams['axes.unicode_minus'] = False
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']

# ==================== 配置参数 ====================
# 数据文件路径
DATA_FILE = "合并指数数据_2014起.xlsx"

# 资产分组配置（基于学术研究标准）
SAFE_ASSETS = ["COMEX黄金", "Bitcoin"]  # 避险资产候选
RISK_ASSETS_STOCK = ["中证800全收益", "MSCI全球市场股票全收益"]  # 风险资产-股票
RISK_ASSETS_BOND = ["中证全债", "巴克莱彭博全球债"]  # 风险资产-债券
ALL_RISK_ASSETS = RISK_ASSETS_STOCK + RISK_ASSETS_BOND

# 中文资产名称映射（用于结果展示）
ASSET_NAME_MAPPING = {
    "COMEX黄金": "COMEX黄金",
    "Bitcoin": "比特币",
    "MVDA Index": "加密货币指数",
    "中证800全收益": "中证800全收益指数",
    "MSCI全球市场股票全收益": "MSCI全球股票指数",
    "中证全债": "中证全债指数",
    "巴克莱彭博全球债": "巴克莱全球债券指数"
}

# 分析参数（基于Baur & Lucey 2010等研究）
QUANTILES = [0.01, 0.025, 0.05]  # 极端分位数阈值：1%, 2.5%, 5%
EVENT_WINDOW_LENGTH = 16  # 事件窗口长度 [0, +15]
SIGNIFICANCE_LEVEL = 0.05  # 统计显著性水平
RISK_FREE_RATE = 0.00  # 无风险利率（年化）

# 模型配置
GARCH_P = 1  # GARCH模型滞后阶数
GARCH_Q = 1  # ARCH模型滞后阶数
MAX_ITERATIONS = 1000  # 最大迭代次数

# 输出文件配置
TIMESTAMP = datetime.now().strftime("%Y%m%d_%H%M%S")
OUTPUT_EXCEL = f"黄金避险属性分析结果_{TIMESTAMP}.xlsx"
OUTPUT_FOLDER = "避险分析结果"

# 图表配置
FIGURE_DPI = 300
FIGURE_FORMAT = 'png'
CHART_COLORS = {
    'gold': '#FFD700',
    'MVDA Index': '#FF8C00',
    'stock': '#1E90FF',
    'bond': '#32CD32',
    'neutral': '#808080'
}

def load_and_preprocess_data(file_path):
    """
    读取并预处理数据
    完全复用参考脚本中的数据加载和日期补全处理代码

    Parameters:
    -----------
    file_path : str
        数据文件路径

    Returns:
    --------
    pd.DataFrame
        预处理后的数据，包含日期补全和向前填充
    """
    print("📊 正在读取数据文件...")

    try:
        # 读取Excel文件
        raw_data = pd.read_excel(file_path)
        print(f"✓ 成功读取数据，原始数据形状: {raw_data.shape}")

        # 日期列处理
        date_col = raw_data.columns[0]
        print(f"✓ 使用日期列: {date_col}")

        # 转换日期格式
        raw_data[date_col] = pd.to_datetime(raw_data[date_col])
        raw_data = raw_data.set_index(date_col)

        # 重新索引为连续日期并向前填充（完全复用参考脚本方法）
        print("📅 正在进行日期补全处理...")
        index_new = pd.date_range(start=raw_data.index[0], end=raw_data.index[-1], freq='D')
        raw_data = raw_data.reindex(index_new)
        raw_data = raw_data.fillna(method='ffill')

        print(f"✓ 日期补全后数据形状: {raw_data.shape}")
        print(f"✓ 数据日期范围: {raw_data.index[0].strftime('%Y-%m-%d')} 到 {raw_data.index[-1].strftime('%Y-%m-%d')}")

        # 数据质量检查
        missing_data = raw_data.isnull().sum()
        if missing_data.sum() > 0:
            print(f"⚠ 发现缺失数据: {missing_data[missing_data > 0].to_dict()}")
        else:
            print("✓ 数据质量检查通过，无缺失值")

        return raw_data

    except Exception as e:
        print(f"❌ 读取数据时发生错误: {e}")
        return None

def check_asset_availability(data, asset_lists):
    """
    检查资产可用性并提供详细的诊断信息

    Parameters:
    -----------
    data : pd.DataFrame
        原始数据
    asset_lists : dict
        资产分组字典

    Returns:
    --------
    dict
        可用资产字典
    """
    available_columns = data.columns.tolist()
    print(f"\n📋 数据中可用的资产列 (共{len(available_columns)}个):")
    for i, col in enumerate(available_columns, 1):
        print(f"  {i:2d}. {col}")

    results = {}
    total_missing = 0

    for name, assets in asset_lists.items():
        available = [asset for asset in assets if asset in available_columns]
        missing = [asset for asset in assets if asset not in available_columns]

        if missing:
            print(f"⚠ {name} 中缺失资产: {missing}")
            total_missing += len(missing)
        else:
            print(f"✓ {name} 资产完整: {available}")

        results[name] = available

    if total_missing == 0:
        print("✓ 所有配置的资产都可用")
    else:
        print(f"⚠ 总计缺失 {total_missing} 个资产")

    return results

def calculate_returns(data, asset_list):
    """
    计算资产组的收益率
    完全复用参考脚本中的收益率计算方法

    Parameters:
    -----------
    data : pd.DataFrame
        价格数据
    asset_list : list
        资产列表

    Returns:
    --------
    pd.DataFrame
        日收益率数据
    """
    print(f"📈 正在计算收益率，资产: {asset_list}")

    # 检查哪些资产在数据中存在
    available_assets = [asset for asset in asset_list if asset in data.columns and asset.strip() != ""]
    missing_assets = [asset for asset in asset_list if asset not in data.columns or asset.strip() == ""]

    if missing_assets:
        print(f"⚠ 以下资产不存在或为空，将被忽略: {missing_assets}")

    if not available_assets:
        print("❌ 错误：没有可用的资产")
        return None

    print(f"✓ 使用的资产: {available_assets}")

    # 提取相关资产的价格数据
    price_data = data[available_assets].copy()

    # 删除任何包含 NaN 的行
    original_length = len(price_data)
    price_data = price_data.dropna()
    dropped_rows = original_length - len(price_data)

    if dropped_rows > 0:
        print(f"🧹 删除了 {dropped_rows} 行包含 NaN 的数据")

    # 计算日收益率（使用 pct_change 方法）
    returns_data = price_data.pct_change().dropna()

    # 再次检查并删除任何剩余的 NaN 值
    before_final_clean = len(returns_data)
    returns_data = returns_data.dropna()
    final_dropped = before_final_clean - len(returns_data)

    if final_dropped > 0:
        print(f"🧹 计算收益率后删除了额外的 {final_dropped} 行 NaN 数据")

    # 数据统计摘要
    print(f"✓ 收益率计算完成，有效数据点: {len(returns_data)}")
    if len(returns_data) > 0:
        print(f"✓ 数据期间: {returns_data.index[0].strftime('%Y-%m-%d')} 到 {returns_data.index[-1].strftime('%Y-%m-%d')}")

        # 显示基本统计信息
        for asset in available_assets:
            if asset in returns_data.columns:
                mean_return = returns_data[asset].mean() * 252  # 年化
                volatility = returns_data[asset].std() * np.sqrt(252)  # 年化
                print(f"  {ASSET_NAME_MAPPING.get(asset, asset)}: 年化收益率={mean_return:.2%}, 年化波动率={volatility:.2%}")

    return returns_data

def align_data(returns_dict):
    """对齐所有资产组的数据时间范围"""
    print("\n正在对齐数据的时间范围...")
    
    # 找到所有数据集的共同日期
    common_dates = None
    for name, returns in returns_dict.items():
        if returns is not None:
            if common_dates is None:
                common_dates = returns.index
            else:
                common_dates = common_dates.intersection(returns.index)
    
    if common_dates is None or len(common_dates) == 0:
        print("错误：没有共同的时间期间")
        return None
    
    # 对齐数据
    aligned_returns = {}
    for name, returns in returns_dict.items():
        if returns is not None:
            aligned_returns[name] = returns.loc[common_dates]
    
    # 删除任何剩余的 NaN 行
    print("正在删除对齐后的 NaN 数据...")
    
    # 合并所有数据来检查 NaN
    combined_data = pd.concat(aligned_returns.values(), axis=1)
    clean_dates = combined_data.dropna().index
    
    # 使用清洁的日期重新对齐
    final_returns = {}
    for name, returns in aligned_returns.items():
        final_returns[name] = returns.loc[clean_dates]
    
    dropped_dates = len(common_dates) - len(clean_dates)
    if dropped_dates > 0:
        print(f"对齐后删除了 {dropped_dates} 行包含 NaN 的数据")
    
    print(f"最终共同数据期间: {len(clean_dates)}天")
    if len(clean_dates) > 0:
        print(f"分析期间: {clean_dates.min()} 到 {clean_dates.max()}")
    
    return final_returns

def create_interaction_variables(returns, quantiles=QUANTILES):
    """
    创建交互变量用于识别极端市场事件
    基于Baur & Lucey (2010)方法，创建条件交互变量

    Parameters:
    -----------
    returns : pd.Series
        风险资产收益率序列
    quantiles : list
        分位数阈值列表

    Returns:
    --------
    dict
        交互变量字典，包含虚拟变量和交互项
    """
    print(f"🎯 正在创建交互变量，分位数阈值: {[f'{q:.1%}' for q in quantiles]}")

    interactions = {}

    for q in quantiles:
        threshold = returns.quantile(q)
        interaction_name = f'D_{int(q*100)}pct'
        interaction_term_name = f'D_{int(q*100)}pct_interaction'
        # 创建虚拟变量：当收益率低于阈值时为1，否则为0
        dummy = np.where(returns <= threshold, 1, 0)

        # 创建交互项：虚拟变量 × 收益率（用于回归分析）
        interaction_term = np.where(returns <= threshold, returns, 0)

        # 存储虚拟变量和交互项
        interactions[interaction_name] = dummy
        interactions[interaction_term_name] = interaction_term

        # 记录极端事件日期
        extreme_dates = returns[returns <= threshold].index
        interactions[f'{interaction_name}_dates'] = extreme_dates

        # 统计信息
        trigger_count = dummy.sum()
        trigger_rate = trigger_count / len(returns)

        print(f"  📊 {interaction_name}: 阈值={threshold:.4f}, 触发次数={trigger_count}, 触发率={trigger_rate:.2%}")

        # 显示极端事件的统计特征
        extreme_returns = returns[returns <= threshold]
        if len(extreme_returns) > 0:
            print(f"     极端收益率统计: 均值={extreme_returns.mean():.4f}, 标准差={extreme_returns.std():.4f}")

    print(f"✓ 交互变量创建完成，共生成 {len(interactions)} 个变量")
    return interactions

def estimate_garch_model(dependent_var, independent_vars, model_name):
    """
    估计GARCH回归模型，自动降级为OLS
    基于Baur & Lucey (2010)的回归方程：
    R_safe,t = α + β1*R_stock,t + β2*D_q*R_stock,t + ε_t

    Parameters:
    -----------
    dependent_var : pd.Series
        因变量（避险资产收益率）
    independent_vars : pd.DataFrame
        自变量（包含风险资产收益率和交互项）
    model_name : str
        模型名称（用于日志输出）

    Returns:
    --------
    dict
        回归结果字典，包含参数、标准误、t统计量、p值等
    """
    print(f"🔬 正在估计模型: {model_name}")

    try:
        # 首先尝试GARCH模型（如果arch库可用）
        if ARCH_AVAILABLE:
            try:
                # 注意：arch库的多元GARCH实现复杂，这里先用OLS
                print("  ⚠ arch库的多元GARCH较复杂，使用OLS回归")
                raise NotImplementedError("使用OLS替代")
            except Exception as e:
                print(f"  ⚠ GARCH模型失败，降级为OLS: {str(e)[:50]}...")

        # OLS回归（主要方法）
        X = sm.add_constant(independent_vars)
        model = sm.OLS(dependent_var, X)
        results = model.fit()

        params = results.params
        std_errors = results.bse
        t_stats = results.tvalues
        p_values = results.pvalues

        # 计算β1 + β2的联合检验
        beta_sum_test = None
        if len(params) >= 3:  # 至少有常数项、β1、β2
            try:
                # 构建约束矩阵进行联合检验
                constraint_matrix = np.zeros((1, len(params)))
                constraint_matrix[0, 1] = 1  # β1系数
                constraint_matrix[0, 2] = 1  # β2系数

                # 进行Wald检验
                wald_test = results.wald_test(constraint_matrix)
                beta_sum_test = {
                    'beta_sum': params.iloc[1] + params.iloc[2],
                    'p_value': wald_test.pvalue
                }
            except Exception as e:
                print(f"    ⚠ β1+β2联合检验失败: {e}")

        result = {
            'params': params,
            'std_errors': std_errors,
            't_stats': t_stats,
            'p_values': p_values,
            'aic': results.aic,
            'bic': results.bic,
            'r_squared': results.rsquared,
            'adj_r_squared': results.rsquared_adj,
            'f_statistic': results.fvalue,
            'f_pvalue': results.f_pvalue,
            'model_type': 'OLS',
            'beta_sum_test': beta_sum_test,
            'n_obs': results.nobs
        }

        print(f"  ✓ 模型估计完成: R² = {results.rsquared:.4f}, F统计量 = {results.fvalue:.2f}")
        return result

    except Exception as e:
        print(f"❌ 模型估计失败 {model_name}: {e}")
        return None

def determine_hedge_safe_haven_properties(beta1, beta2, p_beta1, p_beta2, p_beta_sum, significance_level=SIGNIFICANCE_LEVEL):
    """
    判定避险属性
    基于Baur & Lucey (2010)的标准：
    - 对冲属性(Hedge): β1 ≤ 0 且统计上不显著 (p > 0.05)
    - 安全港属性(Safe Haven): β1 + β2 ≤ 0 且统计上显著 (p ≤ 0.05)

    Parameters:
    -----------
    beta1 : float
        风险资产收益率系数
    beta2 : float
        交互项系数
    p_beta1 : float
        β1的p值
    p_beta2 : float
        β2的p值
    p_beta_sum : float
        β1+β2联合检验的p值
    significance_level : float
        显著性水平

    Returns:
    --------
    dict
        避险属性判定结果
    """
    # 对冲属性判定：β1 ≤ 0 且统计上不显著
    is_hedge = (beta1 <= 0) and (p_beta1 > significance_level)

    # 安全港属性判定：β1 + β2 ≤ 0 且统计上显著
    beta_sum = beta1 + beta2
    is_safe_haven = (beta_sum <= 0) and (p_beta_sum is not None) and (p_beta_sum <= significance_level)

    # 分类结果
    if is_hedge and is_safe_haven:
        classification = "强避险资产"
        description = "既具有对冲属性又具有安全港属性"
    elif is_hedge:
        classification = "弱避险资产"
        description = "仅具有对冲属性"
    elif is_safe_haven:
        classification = "条件避险资产"
        description = "仅在极端市场条件下具有避险属性"
    else:
        classification = "非避险资产"
        description = "不具有避险属性"

    return {
        'classification': classification,
        'description': description,
        'is_hedge': is_hedge,
        'is_safe_haven': is_safe_haven,
        'beta1': beta1,
        'beta2': beta2,
        'beta_sum': beta_sum,
        'p_beta1': p_beta1,
        'p_beta2': p_beta2,
        'p_beta_sum': p_beta_sum
    }

def calculate_car(asset_returns, benchmark_return, event_dates, window_length=EVENT_WINDOW_LENGTH):
    """
    计算累积异常收益（CAR）
    事件研究方法，分析极端市场事件后的累积异常收益

    Parameters:
    -----------
    asset_returns : pd.Series
        资产收益率序列
    benchmark_return : float
        基准收益率（通常为0或无风险利率）
    event_dates : pd.DatetimeIndex
        事件日期列表
    window_length : int
        事件窗口长度（默认16天，即[0, +15]）

    Returns:
    --------
    dict
        CAR分析结果，包含均值、标准差、t统计量、p值等
    """
    print(f"📊 正在计算累积异常收益，事件数量: {len(event_dates)}, 窗口长度: {window_length}")

    car_results = []
    valid_events = 0
    invalid_events = 0

    for event_date in event_dates:
        if event_date in asset_returns.index:
            # 获取事件窗口的收益率
            try:
                # 找到事件日期在索引中的位置
                event_idx = asset_returns.index.get_loc(event_date)

                # 确保有足够的后续数据
                if event_idx + window_length <= len(asset_returns):
                    window_returns = asset_returns.iloc[event_idx:event_idx + window_length]

                    # 计算异常收益（实际收益 - 基准收益）
                    abnormal_returns = window_returns - benchmark_return

                    # 计算累积异常收益
                    car_series = abnormal_returns.cumsum()
                    car_results.append(car_series.values)
                    valid_events += 1
                else:
                    invalid_events += 1
            except Exception as e:
                invalid_events += 1
                continue
        else:
            invalid_events += 1

    print(f"  ✓ 有效事件: {valid_events}, 无效事件: {invalid_events}")

    if car_results and valid_events > 0:
        # 转换为DataFrame进行统计分析
        car_df = pd.DataFrame(car_results)

        # 计算统计量
        mean_car = car_df.mean()
        std_car = car_df.std()

        # t检验（检验CAR是否显著不为0）
        if valid_events > 1:
            t_stats = mean_car / (std_car / np.sqrt(valid_events))
            p_values = 2 * (1 - stats.t.cdf(np.abs(t_stats), valid_events - 1))
        else:
            t_stats = pd.Series([np.nan] * len(mean_car))
            p_values = pd.Series([np.nan] * len(mean_car))

        # 计算最终CAR（事件窗口结束时的累积异常收益）
        final_car = mean_car.iloc[-1] if hasattr(mean_car, 'iloc') and len(mean_car) > 0 else (mean_car[-1] if len(mean_car) > 0 else np.nan)
        final_car_tstat = t_stats.iloc[-1] if hasattr(t_stats, 'iloc') and len(t_stats) > 0 else (t_stats[-1] if len(t_stats) > 0 else np.nan)
        final_car_pvalue = p_values.iloc[-1] if hasattr(p_values, 'iloc') and len(p_values) > 0 else (p_values[-1] if len(p_values) > 0 else np.nan)

        result = {
            'mean_car': mean_car,
            'std_car': std_car,
            't_stats': t_stats,
            'p_values': p_values,
            'valid_events': valid_events,
            'invalid_events': invalid_events,
            'final_car': final_car,
            'final_car_tstat': final_car_tstat,
            'final_car_pvalue': final_car_pvalue,
            'car_data': car_df  # 原始CAR数据用于进一步分析
        }

        print(f"  ✓ CAR计算完成: 最终CAR = {final_car:.4f}, t统计量 = {final_car_tstat:.2f}, p值 = {final_car_pvalue:.4f}")
        return result
    else:
        print("  ⚠ 没有有效的事件数据用于CAR计算")
        return None

def create_data_overview_sheet(returns_dict, writer):
    """
    创建数据概览工作表
    包含数据基本信息和资产统计特征
    """
    print("📋 正在创建数据概览工作表...")

    # 数据概览信息
    overview_data = []

    for name, returns in returns_dict.items():
        if returns is not None:
            overview_data.append({
                '资产组': name,
                '资产数量': len(returns.columns),
                '数据期间': f"{returns.index[0].strftime('%Y-%m-%d')} 到 {returns.index[-1].strftime('%Y-%m-%d')}",
                '数据点数': len(returns),
                '平均年化收益率': f"{returns.mean().mean() * 252:.2%}",
                '平均年化波动率': f"{returns.std().mean() * np.sqrt(252):.2%}"
            })

    overview_df = pd.DataFrame(overview_data)
    overview_df.to_excel(writer, sheet_name='1-数据概览', index=False)

    # 添加详细资产列表
    asset_list_data = []
    for name, returns in returns_dict.items():
        if returns is not None:
            for asset in returns.columns:
                annual_return = returns[asset].mean() * 252
                annual_vol = returns[asset].std() * np.sqrt(252)
                sharpe = annual_return / annual_vol if annual_vol > 0 else 0

                asset_list_data.append({
                    '资产组': name,
                    '资产名称': ASSET_NAME_MAPPING.get(asset, asset),
                    '原始名称': asset,
                    '年化收益率': f"{annual_return:.2%}",
                    '年化波动率': f"{annual_vol:.2%}",
                    '夏普比率': f"{sharpe:.4f}",
                    '最大值': f"{returns[asset].max():.4f}",
                    '最小值': f"{returns[asset].min():.4f}",
                    '偏度': f"{returns[asset].skew():.4f}",
                    '峰度': f"{returns[asset].kurtosis():.4f}"
                })
    
    asset_list_df = pd.DataFrame(asset_list_data)
    asset_list_df.to_excel(writer, sheet_name='2-资产详情', index=False)

    print("✓ 数据概览工作表创建完成")

def create_garch_results_sheet(garch_results, writer):
    """
    创建GARCH回归结果工作表
    """
    print("📊 正在创建GARCH回归结果工作表...")

    if not garch_results:
        print("⚠ 没有GARCH回归结果")
        return

    # 整理回归结果
    results_data = []
    for model_name, result in garch_results.items():
        if result is not None:
            params = result['params']
            p_values = result['p_values']

            results_data.append({
                '模型名称': model_name,
                '常数项': f"{params.get('const', 0):.6f}",
                '常数项p值': f"{p_values.get('const', 1):.4f}",
                'β1(风险资产系数)': f"{params.get('risk_return', 0):.6f}",
                'β1 p值': f"{p_values.get('risk_return', 1):.4f}",
                'β2(交互项系数)': f"{params.get('interaction', 0):.6f}",
                'β2 p值': f"{p_values.get('interaction', 1):.4f}",
                'R²': f"{result.get('r_squared', 0):.4f}",
                '调整R²': f"{result.get('adj_r_squared', 0):.4f}",
                'F统计量': f"{result.get('f_statistic', 0):.2f}",
                'F p值': f"{result.get('f_pvalue', 1):.4f}",
                'AIC': f"{result.get('aic', 0):.2f}",
                'BIC': f"{result.get('bic', 0):.2f}",
                '观测数': int(result.get('n_obs', 0)),
                '模型类型': result.get('model_type', 'Unknown')
            })

    results_df = pd.DataFrame(results_data)
    results_df.to_excel(writer, sheet_name='3-GARCH回归结果', index=False)

    print("✓ GARCH回归结果工作表创建完成")

def create_hedge_properties_sheet(hedge_results, writer):
    """
    创建避险属性判定结果工作表
    """
    print("🛡️ 正在创建避险属性判定工作表...")

    if not hedge_results:
        print("⚠ 没有避险属性结果")
        return

    # 整理避险属性结果
    properties_data = []
    for safe_asset, risk_results in hedge_results.items():
        for risk_asset, quantile_results in risk_results.items():
            for quantile, result in quantile_results.items():
                properties_data.append({
                    '避险资产': ASSET_NAME_MAPPING.get(safe_asset, safe_asset),
                    '风险资产': ASSET_NAME_MAPPING.get(risk_asset, risk_asset),
                    '分位数阈值': f"{quantile:.1%}",
                    'β1': f"{result['beta1']:.6f}",
                    'β2': f"{result['beta2']:.6f}",
                    'β1+β2': f"{result['beta1'] + result['beta2']:.6f}",
                    'β1 p值': f"{result['p_beta1']:.4f}",
                    'β2 p值': f"{result['p_beta2']:.4f}",
                    'β1+β2 p值': f"{result['p_beta_sum']:.4f}",
                    '对冲属性': '是' if result['is_hedge'] else '否',
                    '安全港属性': '是' if result['is_safe_haven'] else '否',
                    '避险分类': result['hedge_property']['classification'],
                    '属性描述': result['hedge_property']['description']
                })

    properties_df = pd.DataFrame(properties_data)
    properties_df.to_excel(writer, sheet_name='4-避险属性判定', index=False)

    print("✓ 避险属性判定工作表创建完成")

def create_event_study_sheet(event_results, writer):
    """
    创建事件研究结果工作表
    """
    print("📈 正在创建事件研究结果工作表...")

    if not event_results:
        print("⚠ 没有事件研究结果")
        return

    # 整理事件研究结果
    event_data = []
    for safe_asset, result in event_results.items():
        if result is not None:
            event_data.append({
                '避险资产': ASSET_NAME_MAPPING.get(safe_asset, safe_asset),
                '有效事件数': result['valid_events'],
                '无效事件数': result['invalid_events'],
                '最终CAR': f"{result['final_car']:.6f}",
                'CAR t统计量': f"{result['final_car_tstat']:.4f}",
                'CAR p值': f"{result['final_car_pvalue']:.4f}",
                'CAR显著性': '显著' if result['final_car_pvalue'] <= 0.05 else '不显著',
                '事件窗口': f"[0, +{EVENT_WINDOW_LENGTH-1}]"
            })

    event_df = pd.DataFrame(event_data)
    event_df.to_excel(writer, sheet_name='5-事件研究结果', index=False)

    # 添加详细的CAR时间序列数据
    if event_results:
        car_series_data = []
        for safe_asset, result in event_results.items():
            if result is not None and 'mean_car' in result:
                for day, car_value in enumerate(result['mean_car']):
                    car_series_data.append({
                        '避险资产': ASSET_NAME_MAPPING.get(safe_asset, safe_asset),
                        '事件后天数': day,
                        '平均CAR': f"{car_value:.6f}",
                        'CAR标准差': f"{result['std_car'][day] if hasattr(result['std_car'], '__getitem__') else result['std_car']:.6f}",
                        't统计量': f"{result['t_stats'][day] if hasattr(result['t_stats'], '__getitem__') else result['t_stats']:.4f}",
                        'p值': f"{result['p_values'][day] if hasattr(result['p_values'], '__getitem__') else result['p_values']:.4f}"
                    })

        if car_series_data:
            car_series_df = pd.DataFrame(car_series_data)
            car_series_df.to_excel(writer, sheet_name='6-CAR时间序列', index=False)

    print("✓ 事件研究结果工作表创建完成")

def create_summary_sheet(hedge_results, event_results, writer):
    """
    创建分析结果摘要工作表
    """
    print("📋 正在创建分析摘要工作表...")

    # 避险属性摘要
    summary_data = []
    for safe_asset, risk_results in hedge_results.items():
        hedge_count = 0
        safe_haven_count = 0
        strong_count = 0
        total_tests = 0

        for risk_asset, quantile_results in risk_results.items():
            for quantile, result in quantile_results.items():
                total_tests += 1
                if result['is_hedge']:
                    hedge_count += 1
                if result['is_safe_haven']:
                    safe_haven_count += 1
                if result['hedge_property']['classification'] == "强避险资产":
                    strong_count += 1

        # 获取事件研究结果
        event_result = event_results.get(safe_asset, {})
        final_car = event_result.get('final_car', np.nan)
        car_significant = event_result.get('final_car_pvalue', 1) <= 0.05

        summary_data.append({
            '避险资产': ASSET_NAME_MAPPING.get(safe_asset, safe_asset),
            '总测试次数': total_tests,
            '对冲属性次数': hedge_count,
            '对冲属性比例': f"{hedge_count/total_tests*100:.1f}%" if total_tests > 0 else "0%",
            '安全港属性次数': safe_haven_count,
            '安全港属性比例': f"{safe_haven_count/total_tests*100:.1f}%" if total_tests > 0 else "0%",
            '强避险资产次数': strong_count,
            '强避险资产比例': f"{strong_count/total_tests*100:.1f}%" if total_tests > 0 else "0%",
            '最终CAR': f"{final_car:.6f}" if not np.isnan(final_car) else "N/A",
            'CAR显著性': '显著' if car_significant else '不显著',
            '综合评价': get_comprehensive_evaluation(hedge_count, safe_haven_count, strong_count, total_tests, car_significant)
        })

    summary_df = pd.DataFrame(summary_data)
    summary_df.to_excel(writer, sheet_name='7-分析摘要', index=False)

    print("✓ 分析摘要工作表创建完成")

def get_comprehensive_evaluation(hedge_count, safe_haven_count, strong_count, total_tests, car_significant):
    """
    综合评价避险资产属性
    """
    if total_tests == 0:
        return "数据不足"

    hedge_ratio = hedge_count / total_tests
    safe_haven_ratio = safe_haven_count / total_tests
    strong_ratio = strong_count / total_tests

    if strong_ratio >= 0.5:
        evaluation = "优秀避险资产"
    elif hedge_ratio >= 0.6 or safe_haven_ratio >= 0.4:
        evaluation = "良好避险资产"
    elif hedge_ratio >= 0.3 or safe_haven_ratio >= 0.2:
        evaluation = "一般避险资产"
    else:
        evaluation = "避险效果有限"

    if car_significant:
        evaluation += " (事件研究支持)"
    else:
        evaluation += " (事件研究不支持)"

    return evaluation

# 删除重复的函数定义，保留更完整的版本

# 删除重复的函数定义

# 删除重复的函数定义

def plot_extreme_events(interactions_dict, returns_dict):
    """绘制极端事件时间序列图"""
    print("正在绘制极端事件时间序列图...")
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle('极端市场事件时间序列分析', fontsize=16, fontweight='bold')
    
    risk_assets = list(interactions_dict.keys())
    
    for i, risk_asset in enumerate(risk_assets[:4]):  # 最多显示4个风险资产
        row, col = i // 2, i % 2
        ax = axes[row, col]
        returns = None
        # 尝试在所有returns_dict中查找该资产
        for group_returns in returns_dict.values():
            if risk_asset in group_returns.columns:
                returns = group_returns[[risk_asset]]
                break
        if returns is None:
            continue
        # 绘制风险资产收益率
        ax.plot(returns.index, returns.iloc[:, 0], alpha=0.7, label=f'{risk_asset}收益率')
        # 标注极端事件
        for q in QUANTILES:
            interaction_name = f'D_{int(q*100)}pct'
            if interaction_name in interactions_dict[risk_asset]:
                extreme_dates = interactions_dict[risk_asset][f'{interaction_name}_dates']
                if len(extreme_dates) > 0:
                    ax.scatter(extreme_dates, returns.loc[extreme_dates].iloc[:, 0], 
                             alpha=0.8, s=30, label=f'{q*100}%分位数事件')
        ax.set_title(f'{risk_asset}极端事件分析')
        ax.set_xlabel('日期')
        ax.set_ylabel('收益率')
        ax.legend()
        ax.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig(f'极端事件时间序列_{TIMESTAMP}.png', dpi=300, bbox_inches='tight')
    plt.show()

def plot_car_comparison(event_results):
    """绘制CAR趋势对比图"""
    print("正在绘制CAR趋势对比图...")
    
    fig, ax = plt.subplots(figsize=(12, 8))
    
    colors = ['gold', 'orange']
    markers = ['o', 's']
    
    for i, (safe_asset, results) in enumerate(event_results.items()):
        if results is not None:
            days = range(len(results['mean_car']))
            mean_car = results['mean_car']
            std_car = results['std_car']
            
            # 绘制CAR曲线
            ax.plot(days, mean_car * 100, color=colors[i], marker=markers[i], 
                   linewidth=2, markersize=6, label=f'{safe_asset} CAR')
            
            # 添加置信区间
            ax.fill_between(days, 
                          (mean_car - 1.96 * std_car / np.sqrt(results['valid_events'])) * 100,
                          (mean_car + 1.96 * std_car / np.sqrt(results['valid_events'])) * 100,
                          alpha=0.2, color=colors[i])
    
    ax.axhline(y=0, color='black', linestyle='--', alpha=0.5)
    ax.set_xlabel('事件后天数')
    ax.set_ylabel('累积异常收益 (%)')
    ax.set_title('黄金 vs 比特币：极端事件后累积异常收益对比')
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(f'CAR趋势对比_{TIMESTAMP}.png', dpi=300, bbox_inches='tight')
    plt.show()

def plot_hedge_heatmap(hedge_results):
    """绘制避险属性热力图"""
    print("正在绘制避险属性热力图...")
    
    # 准备热力图数据
    safe_assets = list(hedge_results.keys())
    risk_assets = list(hedge_results[safe_assets[0]].keys()) if safe_assets else []
    
    # 创建属性评分矩阵
    hedge_scores = np.zeros((len(safe_assets), len(risk_assets)))
    safe_haven_scores = np.zeros((len(safe_assets), len(risk_assets)))
    
    for i, safe_asset in enumerate(safe_assets):
        for j, risk_asset in enumerate(risk_assets):
            # 计算平均避险属性评分
            hedge_count = 0
            safe_haven_count = 0
            total_tests = 0
            
            for quantile, result in hedge_results[safe_asset][risk_asset].items():
                if result['is_hedge']:
                    hedge_count += 1
                if result['is_safe_haven']:
                    safe_haven_count += 1
                total_tests += 1
            
            if total_tests > 0:
                hedge_scores[i, j] = hedge_count / total_tests
                safe_haven_scores[i, j] = safe_haven_count / total_tests
    
    # 绘制热力图
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # 对冲属性热力图
    sns.heatmap(hedge_scores, annot=True, fmt='.2f', cmap='RdYlGn', 
                xticklabels=risk_assets, yticklabels=safe_assets,
                ax=ax1, cbar_kws={'label': '对冲属性比例'})
    ax1.set_title('对冲属性热力图')
    ax1.set_xlabel('风险资产')
    ax1.set_ylabel('避险资产')
    
    # 安全港属性热力图
    sns.heatmap(safe_haven_scores, annot=True, fmt='.2f', cmap='RdYlGn',
                xticklabels=risk_assets, yticklabels=safe_assets,
                ax=ax2, cbar_kws={'label': '安全港属性比例'})
    ax2.set_title('安全港属性热力图')
    ax2.set_xlabel('风险资产')
    ax2.set_ylabel('避险资产')
    
    plt.tight_layout()
    plt.savefig(f'避险属性热力图_{TIMESTAMP}.png', dpi=300, bbox_inches='tight')
    plt.show()

def main():
    """主函数：执行完整的黄金避险属性分析流程"""
    print("=" * 80)
    print("黄金避险属性分析脚本")
    print("=" * 80)
    print(f"避险资产候选: {SAFE_ASSETS}")
    print(f"风险资产-股票: {RISK_ASSETS_STOCK}")
    print(f"风险资产-债券: {RISK_ASSETS_BOND}")
    print(f"极端分位数阈值: {[f'{q*100}%' for q in QUANTILES]}")
    print("=" * 80)

    # 1. 读取和预处理数据
    data = load_and_preprocess_data(DATA_FILE)
    if data is None:
        print("数据读取失败，程序终止")
        return

    # 2. 检查资产可用性
    asset_lists = {
        '避险资产': SAFE_ASSETS,
        '风险资产-股票': RISK_ASSETS_STOCK,
        '风险资产-债券': RISK_ASSETS_BOND
    }
    available_assets = check_asset_availability(data, asset_lists)

    # 3. 计算收益率
    returns_dict = {}
    for name, assets in available_assets.items():
        if assets:
            returns = calculate_returns(data, assets)
            if returns is not None:
                returns_dict[name] = returns
    
    # 如果没有风险资产-股票组，单独计算
    if '风险资产-股票' not in returns_dict:
        stock_returns = calculate_returns(data, RISK_ASSETS_STOCK)
        if stock_returns is not None:
            returns_dict['风险资产-股票'] = stock_returns

    # 4. 对齐数据
    aligned_returns = align_data(returns_dict)
    if aligned_returns is None:
        print("数据对齐失败，程序终止")
        return

    # 5. 创建交互变量
    print("\n正在创建极端事件交互变量...")
    interactions_dict = {}
    for risk_asset in ALL_RISK_ASSETS:
        if risk_asset in aligned_returns.get('风险资产-股票', pd.DataFrame()).columns:
            returns = aligned_returns['风险资产-股票'][risk_asset]
        elif risk_asset in aligned_returns.get('风险资产-债券', pd.DataFrame()).columns:
            returns = aligned_returns['风险资产-债券'][risk_asset]
        else:
            continue
        
        interactions_dict[risk_asset] = create_interaction_variables(returns, QUANTILES)

    # 6. GARCH回归分析
    print("\n正在进行GARCH回归分析...")
    garch_results = {}
    hedge_results = {}
    
    for safe_asset in SAFE_ASSETS:
        if safe_asset in aligned_returns.get('避险资产', pd.DataFrame()).columns:
            safe_returns = aligned_returns['避险资产'][safe_asset]
            hedge_results[safe_asset] = {}
            
            for risk_asset in ALL_RISK_ASSETS:
                if risk_asset in interactions_dict:
                    risk_returns = None
                    if risk_asset in aligned_returns.get('风险资产-股票', pd.DataFrame()).columns:
                        risk_returns = aligned_returns['风险资产-股票'][risk_asset]
                    elif risk_asset in aligned_returns.get('风险资产-债券', pd.DataFrame()).columns:
                        risk_returns = aligned_returns['风险资产-债券'][risk_asset]
                    
                    if risk_returns is not None:
                        hedge_results[safe_asset][risk_asset] = {}
                        
                        for quantile in QUANTILES:
                            interaction_name = f'D_{int(quantile*100)}pct'
                            if interaction_name in interactions_dict[risk_asset]:
                                interaction_var = interactions_dict[risk_asset][interaction_name]
                                
                                # 准备回归变量
                                X = pd.DataFrame({
                                    'risk_return': risk_returns,
                                    'interaction': interaction_var
                                })
                                
                                # 估计模型
                                model_name = f"{safe_asset}_vs_{risk_asset}_{int(quantile*100)}pct"
                                result = estimate_garch_model(safe_returns, X, model_name)
                                
                                if result is not None:
                                    garch_results[model_name] = result
                                    
                                    # 提取系数和显著性
                                    beta1 = result['params'].get('risk_return', 0)
                                    beta2 = result['params'].get('interaction', 0)
                                    p_beta1 = result['p_values'].get('risk_return', 1)
                                    p_beta2 = result['p_values'].get('interaction', 1)
                                    
                                    # 计算β1+β2的显著性（简化处理）
                                    p_beta_sum = min(p_beta1, p_beta2)  # 简化处理
                                    
                                    # 判定避险属性
                                    hedge_property = determine_hedge_safe_haven_properties(
                                        beta1, beta2, p_beta1, p_beta2, p_beta_sum)
                                    
                                    hedge_results[safe_asset][risk_asset][quantile] = {
                                        'beta1': beta1,
                                        'beta2': beta2,
                                        'p_beta1': p_beta1,
                                        'p_beta2': p_beta2,
                                        'p_beta_sum': p_beta_sum,
                                        'hedge_property': hedge_property,
                                        'is_hedge': (beta1 <= 0) and (p_beta1 > SIGNIFICANCE_LEVEL),
                                        'is_safe_haven': (beta1 + beta2 <= 0) and (p_beta_sum <= SIGNIFICANCE_LEVEL)
                                    }

    # 7. 事件研究分析
    print("\n正在进行事件研究分析...")
    event_results = {}
    
    # 使用5%分位数作为事件定义
    event_quantile = 0.05
    benchmark_return = 0  # 假设无风险利率为0
    
    for safe_asset in SAFE_ASSETS:
        if safe_asset in aligned_returns.get('避险资产', pd.DataFrame()).columns:
            safe_returns = aligned_returns['避险资产'][safe_asset]
            
            # 找到所有风险资产的极端事件日期
            all_event_dates = set()
            for risk_asset in ALL_RISK_ASSETS:
                if risk_asset in interactions_dict:
                    interaction_name = f'D_{int(event_quantile*100)}pct'
                    if interaction_name in interactions_dict[risk_asset]:
                        extreme_dates = interactions_dict[risk_asset][f'{interaction_name}_dates']
                        all_event_dates.update(extreme_dates)
            
            # 计算CAR
            if all_event_dates:
                event_results[safe_asset] = calculate_car(
                    safe_returns, benchmark_return, list(all_event_dates), EVENT_WINDOW_LENGTH)

    # 8. 创建输出文件夹
    if not os.path.exists(OUTPUT_FOLDER):
        os.makedirs(OUTPUT_FOLDER)

    # 9. 保存Excel结果
    print(f"\n正在保存结果到Excel文件: {OUTPUT_EXCEL}")
    with pd.ExcelWriter(OUTPUT_EXCEL, engine='openpyxl') as writer:
        create_data_overview_sheet(aligned_returns, writer)
        create_garch_results_sheet(garch_results, writer)
        create_hedge_properties_sheet(hedge_results, writer)
        create_event_study_sheet(event_results, writer)
        create_summary_sheet(hedge_results, event_results, writer)

    # 10. 绘制可视化图表
    print("\n正在生成可视化图表...")
    plot_extreme_events(interactions_dict, aligned_returns)
    plot_car_comparison(event_results)
    plot_hedge_heatmap(hedge_results)

    # 11. 打印分析摘要
    print("\n" + "=" * 80)
    print("黄金避险属性分析结果摘要")
    print("=" * 80)
    
    for safe_asset, risk_results in hedge_results.items():
        print(f"\n{safe_asset} 避险属性分析:")
        hedge_count = 0
        safe_haven_count = 0
        strong_count = 0
        total_tests = 0
        
        for risk_asset, quantile_results in risk_results.items():
            for quantile, result in quantile_results.items():
                total_tests += 1
                if result['is_hedge']:
                    hedge_count += 1
                if result['is_safe_haven']:
                    safe_haven_count += 1
                if result['hedge_property'] == "强避险资产":
                    strong_count += 1
        
        print(f"  总测试次数: {total_tests}")
        print(f"  对冲属性次数: {hedge_count} ({hedge_count/total_tests*100:.1f}%)")
        print(f"  安全港属性次数: {safe_haven_count} ({safe_haven_count/total_tests*100:.1f}%)")
        print(f"  强避险资产次数: {strong_count} ({strong_count/total_tests*100:.1f}%)")
    
    print("\n" + "=" * 80)
    print("分析完成！")
    print(f"结果文件: {OUTPUT_EXCEL}")
    print(f"图表文件: 极端事件时间序列_{TIMESTAMP}.png")
    print(f"图表文件: CAR趋势对比_{TIMESTAMP}.png")
    print(f"图表文件: 避险属性热力图_{TIMESTAMP}.png")

def plot_extreme_events(interactions_dict, aligned_returns):
    """
    绘制极端事件时间序列图
    """
    print("📊 正在绘制极端事件时间序列图...")

    try:
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('极端市场事件识别与资产收益率时间序列', fontsize=16, fontweight='bold')

        # 选择主要资产进行展示
        main_assets = ['中证800全收益', 'MSCI全球市场股票全收益']
        safe_assets = ['COMEX黄金', 'MVDA Index']

        for i, risk_asset in enumerate(main_assets[:2]):
            if risk_asset in interactions_dict:
                ax = axes[0, i]

                # 获取收益率数据
                if risk_asset in aligned_returns.get('风险资产-股票', pd.DataFrame()).columns:
                    returns = aligned_returns['风险资产-股票'][risk_asset]
                else:
                    continue

                # 绘制收益率时间序列
                ax.plot(returns.index, returns.values, alpha=0.7, color='blue', linewidth=0.5)

                # 标记极端事件
                for q in [0.01, 0.05]:
                    interaction_name = f'D_{int(q*100)}pct'
                    if f'{interaction_name}_dates' in interactions_dict[risk_asset]:
                        extreme_dates = interactions_dict[risk_asset][f'{interaction_name}_dates']
                        extreme_returns = returns.loc[extreme_dates]

                        color = 'red' if q == 0.01 else 'orange'
                        ax.scatter(extreme_dates, extreme_returns,
                                 color=color, alpha=0.8, s=20,
                                 label=f'{q:.1%}分位数极端事件')

                ax.set_title(f'{ASSET_NAME_MAPPING.get(risk_asset, risk_asset)}收益率与极端事件')
                ax.set_ylabel('日收益率')
                ax.legend()
                ax.grid(True, alpha=0.3)

        # 绘制避险资产收益率
        for i, safe_asset in enumerate(safe_assets[:2]):
            if safe_asset in aligned_returns.get('避险资产', pd.DataFrame()).columns:
                ax = axes[1, i]
                returns = aligned_returns['避险资产'][safe_asset]

                ax.plot(returns.index, returns.values,
                       color=CHART_COLORS.get('gold' if 'gold' in safe_asset.lower() else 'MVDA Index', 'blue'),
                       alpha=0.8, linewidth=0.8)
                ax.set_title(f'{ASSET_NAME_MAPPING.get(safe_asset, safe_asset)}收益率时间序列')
                ax.set_ylabel('日收益率')
                ax.grid(True, alpha=0.3)

        plt.tight_layout()
        filename = f"极端事件时间序列_{TIMESTAMP}.png"
        plt.savefig(filename, dpi=FIGURE_DPI, bbox_inches='tight')
        plt.close()

        print(f"✓ 极端事件时间序列图已保存: {filename}")

    except Exception as e:
        print(f"⚠ 绘制极端事件图表失败: {e}")

def plot_car_comparison(event_results):
    """
    绘制CAR趋势对比图
    """
    print("📈 正在绘制CAR趋势对比图...")

    try:
        if not event_results:
            print("⚠ 没有事件研究结果用于绘图")
            return

        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))
        fig.suptitle('累积异常收益(CAR)分析结果', fontsize=16, fontweight='bold')

        # 左图：CAR时间序列
        for safe_asset, result in event_results.items():
            if result is not None and 'mean_car' in result:
                days = range(len(result['mean_car']))
                mean_car = result['mean_car']
                std_car = result['std_car']

                color = CHART_COLORS.get('gold' if 'gold' in safe_asset.lower() else 'MVDA Index', 'blue')
                label = ASSET_NAME_MAPPING.get(safe_asset, safe_asset)

                # 绘制均值线
                ax1.plot(days, mean_car, color=color, linewidth=2, label=label)

                # 绘制置信区间
                ax1.fill_between(days,
                               mean_car - 1.96 * std_car / np.sqrt(result['valid_events']),
                               mean_car + 1.96 * std_car / np.sqrt(result['valid_events']),
                               color=color, alpha=0.2)

        ax1.axhline(y=0, color='black', linestyle='--', alpha=0.5)
        ax1.set_title('CAR时间序列 (95%置信区间)')
        ax1.set_xlabel('事件后天数')
        ax1.set_ylabel('累积异常收益')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 右图：最终CAR对比
        assets = []
        final_cars = []
        colors = []

        for safe_asset, result in event_results.items():
            if result is not None:
                assets.append(ASSET_NAME_MAPPING.get(safe_asset, safe_asset))
                final_cars.append(result['final_car'])
                colors.append(CHART_COLORS.get('gold' if 'gold' in safe_asset.lower() else 'MVDA Index', 'blue'))

        bars = ax2.bar(assets, final_cars, color=colors, alpha=0.7)
        ax2.axhline(y=0, color='black', linestyle='-', alpha=0.5)
        ax2.set_title('最终CAR对比')
        ax2.set_ylabel('最终累积异常收益')

        # 添加数值标签
        for bar, value in zip(bars, final_cars):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + (0.001 if height >= 0 else -0.002),
                    f'{value:.4f}', ha='center', va='bottom' if height >= 0 else 'top')

        plt.tight_layout()
        filename = f"CAR趋势对比_{TIMESTAMP}.png"
        plt.savefig(filename, dpi=FIGURE_DPI, bbox_inches='tight')
        plt.close()

        print(f"✓ CAR趋势对比图已保存: {filename}")

    except Exception as e:
        print(f"⚠ 绘制CAR对比图失败: {e}")

def plot_hedge_heatmap(hedge_results):
    """
    绘制避险属性热力图
    """
    print("🔥 正在绘制避险属性热力图...")

    try:
        if not hedge_results:
            print("⚠ 没有避险属性结果用于绘图")
            return

        # 准备数据
        data_for_heatmap = []
        for safe_asset, risk_results in hedge_results.items():
            for risk_asset, quantile_results in risk_results.items():
                for quantile, result in quantile_results.items():
                    data_for_heatmap.append({
                        '避险资产': ASSET_NAME_MAPPING.get(safe_asset, safe_asset),
                        '风险资产-分位数': f"{ASSET_NAME_MAPPING.get(risk_asset, risk_asset)}-{quantile:.1%}",
                        '对冲属性': 1 if result['is_hedge'] else 0,
                        '安全港属性': 1 if result['is_safe_haven'] else 0
                    })

        if not data_for_heatmap:
            print("⚠ 没有数据用于绘制热力图")
            return

        df = pd.DataFrame(data_for_heatmap)

        # 创建透视表
        hedge_pivot = df.pivot(index='避险资产', columns='风险资产-分位数', values='对冲属性')
        safe_haven_pivot = df.pivot(index='避险资产', columns='风险资产-分位数', values='安全港属性')

        # 绘制热力图
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(14, 10))
        fig.suptitle('避险属性热力图分析', fontsize=16, fontweight='bold')

        # 对冲属性热力图
        sns.heatmap(hedge_pivot, annot=True, cmap='RdYlGn', ax=ax1,
                   cbar_kws={'label': '对冲属性 (1=是, 0=否)'})
        ax1.set_title('对冲属性分布')

        # 安全港属性热力图
        sns.heatmap(safe_haven_pivot, annot=True, cmap='RdYlBu', ax=ax2,
                   cbar_kws={'label': '安全港属性 (1=是, 0=否)'})
        ax2.set_title('安全港属性分布')

        plt.tight_layout()
        filename = f"避险属性热力图_{TIMESTAMP}.png"
        plt.savefig(filename, dpi=FIGURE_DPI, bbox_inches='tight')
        plt.close()

        print(f"✓ 避险属性热力图已保存: {filename}")

    except Exception as e:
        print(f"⚠ 绘制避险属性热力图失败: {e}")

if __name__ == "__main__":
    main()