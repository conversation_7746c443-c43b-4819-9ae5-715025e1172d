#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
有效前沿分析脚本（基于 Riskfolio-Lib）
功能：使用 Riskfolio-Lib 比较两组资产的有效前沿
作者：AI Assistant
日期：2025-07-03
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib
import warnings
warnings.filterwarnings('ignore')

# 导入 Riskfolio-Lib 专业投资组合优化库
import riskfolio as rp

print("已导入 Riskfolio-Lib 专业投资组合优化库")

# 设置中文字体
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
matplotlib.rcParams['axes.unicode_minus'] = False

# ==================== 配置参数 ====================
# 第一组资产（包含黄金的组合）
# MVDA Index	Ethereum	Ripple	MSCI全球市场股票全收益	中证800全收益	中证全债	巴克莱彭博全球债	COMEX黄金	NYMEX WTI原油	美元指数
ASSET_GROUP_1 = ["巴克莱彭博全球债", "MSCI全球市场股票全收益"]
GROUP_1_NAME = "股债组合"

# 第二组资产（仅传统资产）
ASSET_GROUP_2 = ["巴克莱彭博全球债", "MSCI全球市场股票全收益", "COMEX黄金"]
GROUP_2_NAME = "股债金组合"

# 第三组资产（可选，如果不需要可以设为 None 或空列表）
ASSET_GROUP_3 = ["巴克莱彭博全球债", "MSCI全球市场股票全收益", "MVDA Index"]  # 设为 None 或 [] 可禁用第三组
GROUP_3_NAME = "股债币组合"
# ASSET_GROUP_3 = []

# 分析参数
NUM_PORTFOLIOS = 50  # 有效前沿点数
RISK_FREE_RATE = 0.00  # 无风险利率（年化）

# 时间区间设置
START_DATE = "2015-01-01"  # 分析开始时间，格式：YYYY-MM-DD（设为None使用全部数据）
# START_DATE = None  # 取消注释此行使用全部数据

# 数据文件路径
DATA_FILE = "合并指数数据_2014起.xlsx"

# 输出文件路径
OUTPUT_FILE = f"有效前沿分析_Riskfolio_{GROUP_1_NAME}_vs_{GROUP_2_NAME}.xlsx"

def load_and_preprocess_data(file_path, start_date=None):
    """读取并预处理数据"""
    print("正在读取数据文件...")
    
    try:
        # 读取Excel文件
        raw_data = pd.read_excel(file_path)
        print(f"成功读取数据，原始数据形状: {raw_data.shape}")
        
        # 日期列处理
        date_col = raw_data.columns[0]
        print(f"使用日期列: {date_col}")
        
        # 转换日期格式
        raw_data[date_col] = pd.to_datetime(raw_data[date_col])
        raw_data = raw_data.set_index(date_col)
        
        # 如果指定了开始日期，则筛选数据
        if start_date is not None:
            start_date = pd.to_datetime(start_date)
            original_length = len(raw_data)
            raw_data = raw_data[raw_data.index >= start_date]
            filtered_length = len(raw_data)
            print(f"应用开始日期筛选 ({start_date.strftime('%Y-%m-%d')})：从 {original_length} 行减少到 {filtered_length} 行")
        
        # 重新索引为连续日期并向前填充
        print("正在进行日期补全处理...")
        index_new = pd.date_range(start=raw_data.index[0], end=raw_data.index[-1], freq='D') 
        raw_data = raw_data.reindex(index_new)  
        raw_data = raw_data.fillna(method='ffill')
        
        print(f"日期补全后数据形状: {raw_data.shape}")
        print(f"数据日期范围: {raw_data.index[0]} 到 {raw_data.index[-1]}")
        
        return raw_data
        
    except Exception as e:
        print(f"读取数据时发生错误: {e}")
        return None

def check_asset_groups(data, group1, group2, group3=None):
    """检查指定的资产组是否存在于数据中"""
    available_columns = data.columns.tolist()
    
    group1_missing = [asset for asset in group1 if asset not in available_columns]
    group2_missing = [asset for asset in group2 if asset not in available_columns]
    
    group3_complete = True
    if group3 is not None and len(group3) > 0:
        group3_missing = [asset for asset in group3 if asset not in available_columns]
        if group3_missing:
            print(f"第三组缺失资产: {group3_missing}")
            group3_complete = False
    else:
        group3_missing = []
    
    if group1_missing:
        print(f"第一组缺失资产: {group1_missing}")
    if group2_missing:
        print(f"第二组缺失资产: {group2_missing}")
    
    if group1_missing or group2_missing or group3_missing:
        print("\n可用的列名:")
        for i, col in enumerate(available_columns):
            print(f"{i+1}. {col}")
    
    return (len(group1_missing) == 0), (len(group2_missing) == 0), group3_complete, available_columns

def calculate_returns(data, asset_list):
    """计算资产组的收益率"""
    print(f"正在计算收益率，资产: {asset_list}")
    
    # 检查哪些资产在数据中存在
    available_assets = [asset for asset in asset_list if asset in data.columns and asset.strip() != ""]
    missing_assets = [asset for asset in asset_list if asset not in data.columns or asset.strip() == ""]
    
    if missing_assets:
        print(f"警告：以下资产不存在或为空，将被忽略: {missing_assets}")
    
    if not available_assets:
        print("错误：没有可用的资产")
        return None
    
    print(f"使用的资产: {available_assets}")
    
    # 提取相关资产的价格数据
    price_data = data[available_assets].copy()
    
    # 删除任何包含 NaN 的行
    original_length = len(price_data)
    price_data = price_data.dropna()
    dropped_rows = original_length - len(price_data)
    
    if dropped_rows > 0:
        print(f"删除了 {dropped_rows} 行包含 NaN 的数据")
    
    # 计算日收益率
    returns_data = price_data.pct_change().dropna()
    
    # 再次检查并删除任何剩余的 NaN 值
    before_final_clean = len(returns_data)
    returns_data = returns_data.dropna()
    final_dropped = before_final_clean - len(returns_data)
    
    if final_dropped > 0:
        print(f"计算收益率后删除了额外的 {final_dropped} 行 NaN 数据")
    
    print(f"收益率计算完成，有效数据点: {len(returns_data)}")
    if len(returns_data) > 0:
        print(f"数据期间: {returns_data.index[0].strftime('%Y-%m-%d')} 到 {returns_data.index[-1].strftime('%Y-%m-%d')}")
    
    return returns_data

def analyze_portfolio_riskfolio(returns, group_name):
    """使用 Riskfolio-Lib 分析投资组合"""
    print(f"正在分析 {group_name}...")
    
    try:
        # 创建投资组合对象
        port = rp.Portfolio(returns=returns)
        
        # 计算基本统计量
        port.assets_stats(method_mu='hist', method_cov='hist')
        
        # 生成有效前沿
        w_frontier = port.efficient_frontier(model='Classic', rm='MV', points=NUM_PORTFOLIOS, rf=0, hist=True)
        
        # 找到特殊投资组合
        # 最小风险组合
        # w_min_risk = port.optimization(model='Classic', rm='MV', obj='MinRisk', rf=0, l=0, hist=True)
        
        # 最大夏普比率组合
        w_max_sharpe = port.optimization(model='Classic', rm='MV', obj='Sharpe', rf=0, l=0, hist=True)
        
        # 计算结果
        results = {
            'frontier_weights': w_frontier,
            'min_risk_weights': None,  # w_min_risk,
            'max_sharpe_weights': w_max_sharpe,
            'returns': returns,
            'portfolio_object': port
        }
        
        print(f"{group_name} 分析完成")
        return results
        
    except Exception as e:
        print(f"分析 {group_name} 时发生错误: {e}")
        print("使用简单方法作为备用...")
        return analyze_portfolio_simple(returns, group_name)

def analyze_portfolio_simple(returns, group_name):
    """简单分析方法（备用）"""
    print(f"使用简单方法分析 {group_name}...")
    
    # 计算年化统计量
    annual_returns = returns.mean() * 252
    annual_cov = returns.cov() * 252
    
    # 生成随机权重的投资组合
    num_assets = len(returns.columns)
    portfolios = []
    
    for _ in range(NUM_PORTFOLIOS):
        weights = np.random.random(num_assets)
        weights = weights / weights.sum()
        
        portfolio_return = np.sum(annual_returns * weights)
        portfolio_volatility = np.sqrt(np.dot(weights.T, np.dot(annual_cov, weights)))
        
        portfolios.append({
            'weights': weights,
            'return': portfolio_return,
            'volatility': portfolio_volatility,
            'sharpe': (portfolio_return - RISK_FREE_RATE) / portfolio_volatility
        })
    
    # 找到最小风险组合
    # min_risk_portfolio = min(portfolios, key=lambda x: x['volatility'])
    
    # 找到最大夏普比率组合  
    max_sharpe_portfolio = max(portfolios, key=lambda x: x['sharpe'])
    
    # 创建权重DataFrame
    frontier_weights = pd.DataFrame([p['weights'] for p in portfolios], 
                                   columns=returns.columns).T
    
    results = {
        'frontier_weights': frontier_weights,
        'min_risk_weights': None,  # pd.DataFrame(min_risk_portfolio['weights'].reshape(-1, 1), index=returns.columns),
        'max_sharpe_weights': pd.DataFrame(max_sharpe_portfolio['weights'].reshape(-1, 1), 
                                         index=returns.columns),
        'returns': returns,
        'portfolios': portfolios
    }
    
    print(f"{group_name} 简单分析完成")
    return results

def calculate_portfolio_performance(weights, returns):
    """计算投资组合绩效"""
    if isinstance(weights, pd.DataFrame):
        if weights.shape[1] == 1:
            w = weights.iloc[:, 0]
        else:
            # 对于有效前沿，计算每个组合的绩效
            performances = []
            for i in range(weights.shape[1]):
                w = weights.iloc[:, i]
                ret, vol = calculate_single_portfolio_performance(w, returns)
                performances.append({'return': ret, 'volatility': vol})
            return performances
    else:
        w = weights
    
    return calculate_single_portfolio_performance(w, returns)

def calculate_single_portfolio_performance(weights, returns):
    """计算单个投资组合的绩效"""
    portfolio_return = (weights * returns.mean()).sum() * 252  # 年化
    portfolio_volatility = np.sqrt((weights.T @ returns.cov() @ weights)) * np.sqrt(252)  # 年化
    return portfolio_return, portfolio_volatility

def plot_efficient_frontiers(results1, results2, group1_name, group2_name, results3=None, group3_name=None):
    """绘制有效前沿对比图（支持最多三组）"""
    print("正在绘制有效前沿对比图...")
    
    fig, ax = plt.subplots(figsize=(12, 8))
    
    # 定义颜色
    colors = ['blue', 'red', 'green']
    
    # 绘制第一组有效前沿
    frontier1 = results1['frontier_weights']
    if frontier1 is not None:
        performances1 = calculate_portfolio_performance(frontier1, results1['returns'])
        if isinstance(performances1, list):
            rets1 = [p['return'] for p in performances1]
            vols1 = [p['volatility'] for p in performances1]
            ax.plot([v*100 for v in vols1], [r*100 for r in rets1], 
                   color=colors[0], linestyle='-', linewidth=2, label=f'{group1_name}有效前沿', alpha=0.8)
    
    # 绘制第二组有效前沿  
    frontier2 = results2['frontier_weights']
    if frontier2 is not None:
        performances2 = calculate_portfolio_performance(frontier2, results2['returns'])
        if isinstance(performances2, list):
            rets2 = [p['return'] for p in performances2]
            vols2 = [p['volatility'] for p in performances2]
            ax.plot([v*100 for v in vols2], [r*100 for r in rets2], 
                   color=colors[1], linestyle='-', linewidth=2, label=f'{group2_name}有效前沿', alpha=0.8)
    
    # 绘制第三组有效前沿（如果存在）
    if results3 is not None and group3_name is not None:
        frontier3 = results3['frontier_weights']
        if frontier3 is not None:
            performances3 = calculate_portfolio_performance(frontier3, results3['returns'])
            if isinstance(performances3, list):
                rets3 = [p['return'] for p in performances3]
                vols3 = [p['volatility'] for p in performances3]
                ax.plot([v*100 for v in vols3], [r*100 for r in rets3], 
                       color=colors[2], linestyle='-', linewidth=2, label=f'{group3_name}有效前沿', alpha=0.8)
    
    # 绘制特殊投资组合点
    # 最小风险组合（注释掉）
    # if results1['min_risk_weights'] is not None:
    #     ret, vol = calculate_portfolio_performance(results1['min_risk_weights'], results1['returns'])
    #     ax.scatter(vol*100, ret*100, color='blue', marker='s', s=100, 
    #               label=f'{group1_name}最小风险', edgecolors='black')
    
    # 最大夏普比率组合
    if results1['max_sharpe_weights'] is not None:
        ret, vol = calculate_portfolio_performance(results1['max_sharpe_weights'], results1['returns'])
        ax.scatter(vol*100, ret*100, color=colors[0], marker='*', s=150, 
                  label=f'{group1_name}最大夏普', edgecolors='black')
    
    if results2['max_sharpe_weights'] is not None:
        ret, vol = calculate_portfolio_performance(results2['max_sharpe_weights'], results2['returns'])
        ax.scatter(vol*100, ret*100, color=colors[1], marker='*', s=150, 
                  label=f'{group2_name}最大夏普', edgecolors='black')
    
    # 第三组最大夏普比率组合
    if results3 is not None and results3['max_sharpe_weights'] is not None:
        ret, vol = calculate_portfolio_performance(results3['max_sharpe_weights'], results3['returns'])
        ax.scatter(vol*100, ret*100, color=colors[2], marker='*', s=150, 
                  label=f'{group3_name}最大夏普', edgecolors='black')
    
    # 设置图形属性
    ax.set_xlabel('年化波动率 (%)', fontsize=12)
    ax.set_ylabel('年化收益率 (%)', fontsize=12)
    ax.set_title('有效前沿对比分析', fontsize=14, fontweight='bold')
    ax.legend(fontsize=10, loc='upper left')
    ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 生成文件名
    if results3 is not None:
        filename = f'有效前沿对比_{group1_name}_vs_{group2_name}_vs_{group3_name}.png'
    else:
        filename = f'有效前沿对比_{group1_name}_vs_{group2_name}.png'
    
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    plt.show()
    
    print("有效前沿对比图已生成并保存")

def save_results_to_excel(results1, results2, group1_name, group2_name, asset_group1, asset_group2, results3=None, group3_name=None, asset_group3=None):
    """显示结果信息（不保存文件）"""
    print(f"结果摘要（不保存文件）:")
    
    try:
        # 显示权重信息（只显示最大夏普组合）
        print(f"\n{group1_name} 权重分配:")
        if results1['max_sharpe_weights'] is not None:
            print("  最大夏普组合权重:")
            weights = results1['max_sharpe_weights'].iloc[:, 0]
            for asset, weight in weights.items():
                print(f"    {asset}: {weight*100:.2f}%")
        
        print(f"\n{group2_name} 权重分配:")
        if results2['max_sharpe_weights'] is not None:
            print("  最大夏普组合权重:")
            weights = results2['max_sharpe_weights'].iloc[:, 0]
            for asset, weight in weights.items():
                print(f"    {asset}: {weight*100:.2f}%")
        
        # 显示第三组（如果存在）
        if results3 is not None and group3_name is not None:
            print(f"\n{group3_name} 权重分配:")
            if results3['max_sharpe_weights'] is not None:
                print("  最大夏普组合权重:")
                weights = results3['max_sharpe_weights'].iloc[:, 0]
                for asset, weight in weights.items():
                    print(f"    {asset}: {weight*100:.2f}%")
        
        print("\n所有结果已在控制台显示（未保存文件）")
        
    except Exception as e:
        print(f"显示结果时发生错误: {e}")

def print_analysis_summary(results1, results2, group1_name, group2_name, results3=None, group3_name=None):
    """打印分析结果摘要"""
    print("\n" + "=" * 80)
    print("有效前沿分析结果摘要（基于 Riskfolio-Lib）")
    print("=" * 80)
    
    # 第一组摘要（只显示最大夏普组合）
    print(f"\n{group1_name}:")
    if results1['max_sharpe_weights'] is not None:
        ret, vol = calculate_portfolio_performance(results1['max_sharpe_weights'], results1['returns'])
        sharpe = (ret - RISK_FREE_RATE) / vol
        print(f"  最大夏普组合: 收益率={ret*100:.2f}%, 波动率={vol*100:.2f}%, 夏普比率={sharpe:.4f}")
    
    # 第二组摘要（只显示最大夏普组合）
    print(f"\n{group2_name}:")
    if results2['max_sharpe_weights'] is not None:
        ret, vol = calculate_portfolio_performance(results2['max_sharpe_weights'], results2['returns'])
        sharpe = (ret - RISK_FREE_RATE) / vol
        print(f"  最大夏普组合: 收益率={ret*100:.2f}%, 波动率={vol*100:.2f}%, 夏普比率={sharpe:.4f}")
    
    # 第三组摘要（如果存在）
    if results3 is not None and group3_name is not None:
        print(f"\n{group3_name}:")
        if results3['max_sharpe_weights'] is not None:
            ret, vol = calculate_portfolio_performance(results3['max_sharpe_weights'], results3['returns'])
            sharpe = (ret - RISK_FREE_RATE) / vol
            print(f"  最大夏普组合: 收益率={ret*100:.2f}%, 波动率={vol*100:.2f}%, 夏普比率={sharpe:.4f}")
    
    print("\n" + "=" * 80)

def main():
    """主函数：执行完整的有效前沿分析流程"""
    print("=" * 80)
    print("有效前沿分析脚本（基于 Riskfolio-Lib）")
    print("=" * 80)
    print(f"第一组资产 ({GROUP_1_NAME}): {ASSET_GROUP_1}")
    print(f"第二组资产 ({GROUP_2_NAME}): {ASSET_GROUP_2}")
    if ASSET_GROUP_3 and len(ASSET_GROUP_3) > 0:
        print(f"第三组资产 ({GROUP_3_NAME}): {ASSET_GROUP_3}")
    print("=" * 80)

    # 1. 读取和预处理数据
    if START_DATE is not None:
        print(f"分析开始日期设置为: {START_DATE}")
    else:
        print("使用全部数据进行分析")
        
    data = load_and_preprocess_data(DATA_FILE, START_DATE)
    if data is None:
        print("数据读取失败，程序终止")
        return

    # 2. 检查资产组是否存在
    group1_complete, group2_complete, group3_complete, available_columns = check_asset_groups(
        data, ASSET_GROUP_1, ASSET_GROUP_2, ASSET_GROUP_3)

    if not (group1_complete and group2_complete):
        print("指定的前两组资产不完整，请检查配置参数")
        return

    # 3. 计算收益率
    returns1 = calculate_returns(data, ASSET_GROUP_1)
    returns2 = calculate_returns(data, ASSET_GROUP_2)
    
    # 第三组（如果启用）
    returns3 = None
    if ASSET_GROUP_3 and len(ASSET_GROUP_3) > 0 and group3_complete:
        returns3 = calculate_returns(data, ASSET_GROUP_3)

    # 检查收益率计算是否成功
    if returns1 is None or returns2 is None:
        print("收益率计算失败，程序终止")
        return
    
    if len(returns1) == 0 or len(returns2) == 0:
        print("收益率数据为空，程序终止")
        return

    # 确保所有数据的时间范围一致
    print("\n正在对齐数据的时间范围...")
    common_dates = returns1.index.intersection(returns2.index)
    
    # 如果有第三组，也要包括在对齐中
    if returns3 is not None:
        common_dates = common_dates.intersection(returns3.index)
    
    if len(common_dates) == 0:
        print("错误：资产组没有共同的时间期间")
        return
    
    # 对齐数据
    returns1_aligned = returns1.loc[common_dates]
    returns2_aligned = returns2.loc[common_dates]
    returns3_aligned = returns3.loc[common_dates] if returns3 is not None else None
    
    # 删除任何剩余的 NaN 行
    print("正在删除对齐后的 NaN 数据...")
    
    # 合并所有组数据来检查 NaN
    data_to_combine = [returns1_aligned, returns2_aligned]
    if returns3_aligned is not None:
        data_to_combine.append(returns3_aligned)
    
    combined_data = pd.concat(data_to_combine, axis=1)
    clean_dates = combined_data.dropna().index
    
    # 使用清洁的日期重新对齐
    returns1 = returns1_aligned.loc[clean_dates]
    returns2 = returns2_aligned.loc[clean_dates]
    if returns3_aligned is not None:
        returns3 = returns3_aligned.loc[clean_dates]
    
    dropped_dates = len(common_dates) - len(clean_dates)
    if dropped_dates > 0:
        print(f"对齐后删除了 {dropped_dates} 行包含 NaN 的数据")

    print(f"\n最终共同数据期间: {len(clean_dates)}天")
    if len(clean_dates) > 0:
        print(f"分析期间: {clean_dates.min()} 到 {clean_dates.max()}")
    else:
        print("错误：清洁后没有可用的数据")
        return

    # 4. 分析投资组合
    results1 = analyze_portfolio_riskfolio(returns1, GROUP_1_NAME)
    results2 = analyze_portfolio_riskfolio(returns2, GROUP_2_NAME)
    
    # 分析第三组（如果存在）
    results3 = None
    if returns3 is not None:
        results3 = analyze_portfolio_riskfolio(returns3, GROUP_3_NAME)

    # 5. 绘制有效前沿对比图
    plot_efficient_frontiers(results1, results2, GROUP_1_NAME, GROUP_2_NAME, 
                            results3, GROUP_3_NAME if results3 is not None else None)

    # 6. 显示结果
    save_results_to_excel(results1, results2, GROUP_1_NAME, GROUP_2_NAME, 
                         ASSET_GROUP_1, ASSET_GROUP_2,
                         results3, GROUP_3_NAME if results3 is not None else None, 
                         ASSET_GROUP_3 if results3 is not None else None)

    # 7. 打印分析摘要
    print_analysis_summary(results1, results2, GROUP_1_NAME, GROUP_2_NAME,
                          results3, GROUP_3_NAME if results3 is not None else None)

    print(f"\n分析完成！")
    if results3 is not None:
        print("图片文件: 有效前沿对比_{}_vs_{}_vs_{}.png".format(GROUP_1_NAME, GROUP_2_NAME, GROUP_3_NAME))
    else:
        print("图片文件: 有效前沿对比_Riskfolio_{}_vs_{}.png".format(GROUP_1_NAME, GROUP_2_NAME))

if __name__ == "__main__":
    main() 